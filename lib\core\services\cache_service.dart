import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class CacheService {
  static const String _prefix = 'cache_';
  static const Duration _defaultTtl = Duration(minutes: 30);
  
  final SharedPreferences _prefs;
  
  CacheService(this._prefs);
  
  // Cache with TTL (Time To Live)
  Future<void> set<T>(String key, T data, {Duration? ttl}) async {
    final cacheKey = _prefix + key;
    final expiry = DateTime.now().add(ttl ?? _defaultTtl);
    
    final cacheData = {
      'data': data,
      'expiry': expiry.millisecondsSinceEpoch,
    };
    
    await _prefs.setString(cacheKey, jsonEncode(cacheData));
  }
  
  // Get cached data
  T? get<T>(String key) {
    final cacheKey = _prefix + key;
    final cachedString = _prefs.getString(cacheKey);
    
    if (cachedString == null) return null;
    
    try {
      final cacheData = jsonDecode(cachedString);
      final expiry = DateTime.fromMillisecondsSinceEpoch(cacheData['expiry']);
      
      // Check if expired
      if (DateTime.now().isAfter(expiry)) {
        remove(key);
        return null;
      }
      
      return cacheData['data'] as T;
    } catch (e) {
      // Invalid cache data, remove it
      remove(key);
      return null;
    }
  }
  
  // Remove cached data
  Future<void> remove(String key) async {
    final cacheKey = _prefix + key;
    await _prefs.remove(cacheKey);
  }
  
  // Clear all cache
  Future<void> clear() async {
    final keys = _prefs.getKeys().where((key) => key.startsWith(_prefix));
    for (final key in keys) {
      await _prefs.remove(key);
    }
  }
  
  // Check if key exists and not expired
  bool has(String key) {
    return get<dynamic>(key) != null;
  }
  
  // Get cache size (number of cached items)
  int get size {
    return _prefs.getKeys().where((key) => key.startsWith(_prefix)).length;
  }
  
  // Cache specific data types
  Future<void> cacheList<T>(String key, List<T> list, {Duration? ttl}) async {
    await set(key, list.map((item) => item.toString()).toList(), ttl: ttl);
  }
  
  List<T>? getCachedList<T>(String key, T Function(String) fromString) {
    final cached = get<List<dynamic>>(key);
    if (cached == null) return null;
    
    try {
      return cached.map((item) => fromString(item.toString())).toList();
    } catch (e) {
      remove(key);
      return null;
    }
  }
  
  // Cache JSON serializable objects
  Future<void> cacheJson(String key, Map<String, dynamic> json, {Duration? ttl}) async {
    await set(key, json, ttl: ttl);
  }
  
  Map<String, dynamic>? getCachedJson(String key) {
    return get<Map<String, dynamic>>(key);
  }
}

// Cache provider
final cacheServiceProvider = Provider<CacheService>((ref) {
  throw UnimplementedError('CacheService must be initialized');
});

// Initialize cache service
Future<Override> initializeCacheService() async {
  final prefs = await SharedPreferences.getInstance();
  final cacheService = CacheService(prefs);
  
  return cacheServiceProvider.overrideWithValue(cacheService);
}

// Cache keys
class CacheKeys {
  static const String userProfile = 'user_profile';
  static const String shopProfile = 'shop_profile';
  static const String driverProfile = 'driver_profile';
  static const String categories = 'categories';
  static const String popularShops = 'popular_shops';
  static const String featuredProducts = 'featured_products';
  static const String appConfig = 'app_config';
  static const String locationHistory = 'location_history';
  
  // Dynamic keys
  static String shopDetail(String shopId) => 'shop_detail_$shopId';
  static String shopProducts(String shopId) => 'shop_products_$shopId';
  static String productDetail(String productId) => 'product_detail_$productId';
  static String userOrders(String userId) => 'user_orders_$userId';
  static String shopOrders(String shopId) => 'shop_orders_$shopId';
  static String driverOrders(String driverId) => 'driver_orders_$driverId';
}

// Cache TTL configurations
class CacheTtl {
  static const Duration short = Duration(minutes: 5);
  static const Duration medium = Duration(minutes: 30);
  static const Duration long = Duration(hours: 2);
  static const Duration veryLong = Duration(hours: 24);
  
  // Specific TTLs
  static const Duration userProfile = medium;
  static const Duration shopProfile = medium;
  static const Duration categories = veryLong;
  static const Duration popularShops = long;
  static const Duration featuredProducts = long;
  static const Duration shopDetail = medium;
  static const Duration productDetail = medium;
  static const Duration orders = short;
  static const Duration appConfig = veryLong;
}

// Cache manager for complex caching strategies
class CacheManager {
  final CacheService _cacheService;
  
  CacheManager(this._cacheService);
  
  // Cache with automatic refresh
  Future<T> getOrFetch<T>(
    String key,
    Future<T> Function() fetcher, {
    Duration? ttl,
    bool forceRefresh = false,
  }) async {
    if (!forceRefresh) {
      final cached = _cacheService.get<T>(key);
      if (cached != null) return cached;
    }
    
    final data = await fetcher();
    await _cacheService.set(key, data, ttl: ttl);
    return data;
  }
  
  // Cache with fallback
  Future<T?> getWithFallback<T>(
    String key,
    Future<T> Function() fetcher, {
    Duration? ttl,
  }) async {
    try {
      final data = await fetcher();
      await _cacheService.set(key, data, ttl: ttl);
      return data;
    } catch (e) {
      // Return cached data if fetch fails
      return _cacheService.get<T>(key);
    }
  }
  
  // Invalidate related caches
  Future<void> invalidatePattern(String pattern) async {
    final prefs = _cacheService._prefs;
    final keys = prefs.getKeys()
        .where((key) => key.startsWith(CacheService._prefix + pattern));
    
    for (final key in keys) {
      await prefs.remove(key);
    }
  }
  
  // Preload cache
  Future<void> preloadCache(Map<String, Future<dynamic> Function()> loaders) async {
    final futures = loaders.entries.map((entry) async {
      try {
        final data = await entry.value();
        await _cacheService.set(entry.key, data);
      } catch (e) {
        // Ignore preload errors
      }
    });
    
    await Future.wait(futures);
  }
}

// Cache manager provider
final cacheManagerProvider = Provider<CacheManager>((ref) {
  final cacheService = ref.read(cacheServiceProvider);
  return CacheManager(cacheService);
});

// Extension for easier cache usage
extension CacheExtension on WidgetRef {
  CacheService get cache => read(cacheServiceProvider);
  CacheManager get cacheManager => read(cacheManagerProvider);
  
  Future<T> cached<T>(
    String key,
    Future<T> Function() fetcher, {
    Duration? ttl,
    bool forceRefresh = false,
  }) {
    return cacheManager.getOrFetch(key, fetcher, ttl: ttl, forceRefresh: forceRefresh);
  }
}
