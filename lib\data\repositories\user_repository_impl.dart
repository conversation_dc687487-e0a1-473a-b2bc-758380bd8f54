import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import '../../core/errors/failures.dart';
import '../../domain/repositories/user_repository.dart';
import '../datasources/auth_api_service.dart';
import '../models/user_model.dart';
import '../models/auth_response_model.dart';
import '../models/api_response_model.dart';

class UserRepositoryImpl implements UserRepository {
  final AuthApiService authApiService;

  UserRepositoryImpl({required this.authApiService});

  @override
  Future<Either<Failure, AuthResponseModel>> register(
    String name,
    String email,
    String phone,
    String password,
    String role,
  ) async {
    try {
      final registerData = {
        'name': name,
        'email': email,
        'phone': phone,
        'password': password,
        'password_confirmation': password,
        'role': role,
      };

      // Call API and normalize to AuthResponseModel
      final response = await authApiService.register(registerData);
      if (response.success && response.data != null) {
        final authResponse = AuthResponseModel.fromJson(
          response.data as Map<String, dynamic>,
        );
        return Right(authResponse);
      } else {
        return Left(AuthenticationFailure(response.message));
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 422) {
        return Left(ValidationFailure('Validation error: ${e.response?.data}'));
      }
      return Left(ServerFailure('Register failed: ${e.message}'));
    } catch (e) {
      return Left(ServerFailure('Lỗi đăng ký: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, AuthResponseModel>> login(
    String email,
    String password,
  ) async {
    try {
      final loginData = {'email': email, 'password': password};

      // Call API and normalize to AuthResponseModel
      final response = await authApiService.login(loginData);
      if (response.success && response.data != null) {
        final authResponse = AuthResponseModel.fromJson(
          response.data as Map<String, dynamic>,
        );
        return Right(authResponse);
      } else {
        return Left(AuthenticationFailure(response.message));
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        return Left(AuthenticationFailure('Invalid credentials'));
      }
      return Left(ServerFailure('Login failed: ${e.message}'));
    } catch (e) {
      return Left(ServerFailure('Lỗi đăng nhập: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, void>> logout() async {
    try {
      await authApiService.logout();
      return const Right(null);
    } on DioException catch (e) {
      return Left(ServerFailure('Logout failed: ${e.message}'));
    } catch (e) {
      return Left(ServerFailure('Lỗi đăng xuất: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, UserModel>> getCurrentUser() async {
    try {
      final response = await authApiService.getCurrentUser();

      if (response.success && response.data != null) {
        final user = UserModel.fromJson(response.data);
        return Right(user);
      } else {
        return Left(AuthenticationFailure(response.message));
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        return Left(AuthenticationFailure('Token expired'));
      }
      return Left(ServerFailure('Failed to get user: ${e.message}'));
    } catch (e) {
      return Left(
        ServerFailure('Lỗi lấy thông tin người dùng: ${e.toString()}'),
      );
    }
  }
}
