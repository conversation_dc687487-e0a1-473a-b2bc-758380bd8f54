import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'api_response_model.g.dart';

// Base API Response without generics for simplicity
@JsonSerializable()
class BaseApiResponseModel extends Equatable {
  final bool success;
  final String message;
  final dynamic data;
  final Map<String, dynamic>? errors;

  const BaseApiResponseModel({
    required this.success,
    required this.message,
    this.data,
    this.errors,
  });

  factory BaseApiResponseModel.fromJson(Map<String, dynamic> json) {
    // First parse with generated mapper
    final base = _$BaseApiResponseModelFromJson(json);

    // Some endpoints (e.g., auth/login) may return payload at root level
    // Normalize by moving auth fields into `data` when `data` is null
    final hasAuthAtRoot =
        json.containsKey('user') ||
        json.containsKey('token') ||
        json.containsKey('token_type');

    if (base.data == null && hasAuthAtRoot) {
      final normalizedData = <String, dynamic>{
        if (json['user'] != null) 'user': json['user'],
        if (json['token'] != null) 'token': json['token'],
        if (json['token_type'] != null) 'token_type': json['token_type'],
      };
      return BaseApiResponseModel(
        success: base.success,
        message: base.message,
        data: normalizedData,
        errors: base.errors,
      );
    }

    return base;
  }

  Map<String, dynamic> toJson() => _$BaseApiResponseModelToJson(this);

  @override
  List<Object?> get props => [success, message, data, errors];
}

// Simple response for operations that don't return data
@JsonSerializable()
class SimpleApiResponseModel extends Equatable {
  final bool success;
  final String message;
  final Map<String, dynamic>? errors;

  const SimpleApiResponseModel({
    required this.success,
    required this.message,
    this.errors,
  });

  factory SimpleApiResponseModel.fromJson(Map<String, dynamic> json) =>
      _$SimpleApiResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$SimpleApiResponseModelToJson(this);

  @override
  List<Object?> get props => [success, message, errors];
}

// Paginated response model
@JsonSerializable()
class PaginatedResponseModel extends Equatable {
  final bool success;
  final String message;
  final PaginatedDataModel? data;
  final Map<String, dynamic>? errors;

  const PaginatedResponseModel({
    required this.success,
    required this.message,
    this.data,
    this.errors,
  });

  factory PaginatedResponseModel.fromJson(Map<String, dynamic> json) =>
      _$PaginatedResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$PaginatedResponseModelToJson(this);

  @override
  List<Object?> get props => [success, message, data, errors];
}

@JsonSerializable()
class PaginatedDataModel extends Equatable {
  @JsonKey(name: 'current_page', fromJson: _nullableIntFromAny)
  final int? currentPage;
  final List<dynamic>? data;
  @JsonKey(name: 'first_page_url')
  final String? firstPageUrl;
  @JsonKey(fromJson: _nullableIntFromAny)
  final int? from;
  @JsonKey(name: 'last_page', fromJson: _nullableIntFromAny)
  final int? lastPage;
  @JsonKey(name: 'last_page_url')
  final String? lastPageUrl;
  @JsonKey(name: 'next_page_url')
  final String? nextPageUrl;
  final String? path;
  @JsonKey(name: 'per_page', fromJson: _nullableIntFromAny)
  final int? perPage;
  @JsonKey(name: 'prev_page_url')
  final String? prevPageUrl;
  @JsonKey(fromJson: _nullableIntFromAny)
  final int? to;
  @JsonKey(fromJson: _nullableIntFromAny)
  final int? total;

  const PaginatedDataModel({
    this.currentPage,
    this.data,
    this.firstPageUrl,
    this.from,
    this.lastPage,
    this.lastPageUrl,
    this.nextPageUrl,
    this.path,
    this.perPage,
    this.prevPageUrl,
    this.to,
    this.total,
  });

  factory PaginatedDataModel.fromJson(Map<String, dynamic> json) =>
      _$PaginatedDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$PaginatedDataModelToJson(this);

  @override
  List<Object?> get props => [
    currentPage,
    data,
    firstPageUrl,
    from,
    lastPage,
    lastPageUrl,
    nextPageUrl,
    path,
    perPage,
    prevPageUrl,
    to,
    total,
  ];
}

// Robust helper for nullable int from any
int? _nullableIntFromAny(dynamic value) {
  if (value == null) return null;
  if (value is int) return value;
  if (value is double) return value.toInt();
  if (value is String) {
    final i = int.tryParse(value);
    if (i != null) return i;
    final d = double.tryParse(value);
    if (d != null) return d.toInt();
  }
  return null;
}
