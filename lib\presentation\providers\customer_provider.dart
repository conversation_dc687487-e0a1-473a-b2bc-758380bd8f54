import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:equatable/equatable.dart';
import '../../data/datasources/api_service_provider.dart';
import '../../data/models/shop_model.dart';
import '../../data/models/product_model.dart';
import '../../data/models/order_model.dart';
import '../../core/providers/loading_provider.dart';
import '../../data/models/api_response_model.dart';
import '../../data/datasources/customer_api_service.dart';

// Customer shops provider
final customerShopsProvider = FutureProvider.autoDispose
    .family<List<ShopModel>, CustomerShopsParams>((ref, params) async {
      final customerService = ref.read(customerApiServiceProvider);

      final response = await customerService.getShops(
        params.page,
        params.perPage,
        params.search,
        params.categoryId,
      );

      if (response.success && response.data != null) {
        final data = response.data!.data;
        if (data is List) {
          final shops = data
              .map((json) => ShopModel.fromJson(json as Map<String, dynamic>))
              .toList();
          return shops;
        } else {
          throw Exception('Invalid data format: expected List');
        }
      } else {
        throw Exception(response.message);
      }
    });

// Customer categories provider
final customerCategoriesProvider = FutureProvider.autoDispose
    .family<List<CategoryModel>, CustomerCategoriesParams>((ref, params) async {
      final customerService = ref.read(customerApiServiceProvider);

      final response = await customerService.getCategories(
        params.page,
        params.perPage,
      );

      if (response.success && response.data != null) {
        final data = response.data;
        // response.data shape: { data: [ {...category}, ... ], pagination: {...} }
        final list = (data['data'] as List?) ?? const [];
        final categories = list
            .map((json) => CategoryModel.fromJson(json as Map<String, dynamic>))
            .toList();
        return categories;
      } else {
        throw Exception(response.message);
      }
    });

class CustomerCategoriesParams extends Equatable {
  final int? page;
  final int? perPage;
  const CustomerCategoriesParams({this.page, this.perPage});
  @override
  List<Object?> get props => [page, perPage];
}

// Products by category provider
final categoryProductsProvider = FutureProvider.autoDispose
    .family<List<ProductModel>, CategoryProductsParams>((ref, params) async {
      final api = ref.read(customerApiServiceProvider);
      final res = await api.getProductsByCategory(
        params.categoryId,
        params.page,
        params.perPage,
      );
      if (res.success && res.data != null) {
        final dataObj = res.data as Map<String, dynamic>;
        final list = (dataObj['data'] as List?) ?? const [];
        return list
            .map((json) => ProductModel.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        throw Exception(res.message);
      }
    });

class CategoryProductsParams extends Equatable {
  final int categoryId;
  final int? page;
  final int? perPage;
  const CategoryProductsParams({
    required this.categoryId,
    this.page,
    this.perPage,
  });
  @override
  List<Object?> get props => [categoryId, page, perPage];
}

// Customer products provider
final customerProductsProvider = FutureProvider.autoDispose
    .family<List<ProductModel>, CustomerProductsParams>((ref, params) async {
      final customerService = ref.read(customerApiServiceProvider);

      final response = await customerService.getProducts(
        params.page,
        params.perPage,
        params.search,
        null, // category_id
        null, // shop_id
        null, // min_price
        null, // max_price
      );

      if (response.success && response.data != null) {
        final data = response.data!.data;
        if (data is List) {
          final products = data
              .map(
                (json) => ProductModel.fromJson(json as Map<String, dynamic>),
              )
              .toList();
          return products;
        } else {
          throw Exception('Invalid data format: expected List');
        }
      } else {
        throw Exception(response.message);
      }
    });

// Customer orders provider
final customerOrdersProvider = FutureProvider.autoDispose
    .family<List<OrderModel>, CustomerOrdersParams>((ref, params) async {
      final customerService = ref.read(customerApiServiceProvider);

      final response = await customerService.getOrders(
        params.page,
        params.perPage,
        params.status,
      );

      if (response.success && response.data != null) {
        final data = response.data!.data;
        if (data is List) {
          final orders = data
              .map((json) => OrderModel.fromJson(json as Map<String, dynamic>))
              .toList();
          return orders;
        } else {
          throw Exception('Invalid data format: expected List');
        }
      } else {
        throw Exception(response.message);
      }
    });

// Shop detail provider
final shopDetailProvider = FutureProvider.autoDispose.family<ShopModel, String>(
  (ref, shopId) async {
    final customerService = ref.read(customerApiServiceProvider);

    final response = await customerService.getShopDetail(shopId);

    if (response.success && response.data != null) {
      return ShopModel.fromJson(response.data);
    } else {
      throw Exception(response.message);
    }
  },
);

// Shop products provider
final shopProductsProvider = FutureProvider.autoDispose
    .family<List<ProductModel>, ShopProductsParams>((ref, params) async {
      final customerService = ref.read(customerApiServiceProvider);

      final response = await customerService.getShopProducts(
        params.shopId,
        params.page,
        params.perPage,
        null, // search
        null, // category_id
        null, // min_price
        null, // max_price
      );

      if (response.success && response.data != null) {
        final data = response.data!.data;
        if (data is List) {
          final products = data
              .map(
                (json) => ProductModel.fromJson(json as Map<String, dynamic>),
              )
              .toList();
          return products;
        } else {
          throw Exception('Invalid data format: expected List');
        }
      } else {
        throw Exception(response.message);
      }
    });

// Unified Search State
class UnifiedSearchState extends Equatable {
  final List<ProductModel> products;
  final List<ShopModel> shops;
  final bool isLoading;
  final String? error;
  final String query;

  const UnifiedSearchState({
    this.products = const [],
    this.shops = const [],
    this.isLoading = false,
    this.error,
    this.query = '',
  });

  UnifiedSearchState copyWith({
    List<ProductModel>? products,
    List<ShopModel>? shops,
    bool? isLoading,
    String? error,
    String? query,
  }) {
    return UnifiedSearchState(
      products: products ?? this.products,
      shops: shops ?? this.shops,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      query: query ?? this.query,
    );
  }

  @override
  List<Object?> get props => [products, shops, isLoading, error, query];
}

class UnifiedSearchNotifier extends StateNotifier<UnifiedSearchState> {
  final CustomerApiService _api;
  UnifiedSearchNotifier(this._api) : super(const UnifiedSearchState());

  Future<void> searchBoth(String query) async {
    if (query.trim().isEmpty) {
      state = state.copyWith(products: [], shops: [], query: '');
      return;
    }
    state = state.copyWith(isLoading: true, error: null, query: query);
    try {
      final res = await _api.search(
        'both',
        query,
        null, // category_id
        null, // latitude
        null, // longitude
        null, // radius
        20, // per_page
        null, // shop_id
        null, // min_price
        null, // max_price
        null, // min_rating
        null, // max_delivery_fee
        null, // max_minimum_order
        null, // is_open
        null, // sort_by
        null, // sort_order
      );
      if (res.success && res.data != null) {
        final data = res.data as Map<String, dynamic>;
        final productsList = ((data['products']?['data']) as List?) ?? const [];
        final shopsList = ((data['shops']?['data']) as List?) ?? const [];
        final products = productsList
            .map((j) => ProductModel.fromJson(j as Map<String, dynamic>))
            .toList();
        final shops = shopsList
            .map((j) => ShopModel.fromJson(j as Map<String, dynamic>))
            .toList();
        state = state.copyWith(
          products: products,
          shops: shops,
          isLoading: false,
          error: null,
        );
      } else {
        state = state.copyWith(isLoading: false, error: res.message);
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  void clear() {
    state = const UnifiedSearchState();
  }
}

final unifiedSearchProvider =
    StateNotifierProvider<UnifiedSearchNotifier, UnifiedSearchState>((ref) {
      return UnifiedSearchNotifier(ref.read(customerApiServiceProvider));
    });

// Computed providers based on API data
final featuredShopsProvider = FutureProvider.autoDispose<List<ShopModel>>((
  ref,
) async {
  // Fetch first page of shops and compute featured
  const params = CustomerShopsParams(page: 1, perPage: 50);
  final shops = await ref.watch(customerShopsProvider(params).future);
  final featured = shops
      .where((s) => s.isActive && (s.rating ?? 0) >= 4.0)
      .toList();
  featured.sort((a, b) => (b.rating ?? 0).compareTo(a.rating ?? 0));
  return featured.take(10).toList();
});

final topRatedShopsProvider = FutureProvider.autoDispose<List<ShopModel>>((
  ref,
) async {
  const params = CustomerShopsParams(page: 1, perPage: 100);
  final shops = await ref.watch(customerShopsProvider(params).future);
  final topRated = shops.where((s) => s.isActive).toList();
  topRated.sort((a, b) {
    final r = (b.rating ?? 0).compareTo(a.rating ?? 0);
    if (r != 0) return r;
    return (b.reviewCount ?? 0).compareTo(a.reviewCount ?? 0);
  });
  return topRated.take(20).toList();
});

final popularProductsApiProvider =
    FutureProvider.autoDispose<List<ProductModel>>((ref) async {
      const params = CustomerProductsParams(page: 1, perPage: 50);
      final products = await ref.watch(customerProductsProvider(params).future);
      final popular = products.where((p) => p.isAvailable).toList();
      popular.sort((a, b) => a.price.compareTo(b.price));
      return popular.take(10).toList();
    });

final bestSellingProductsApiProvider =
    FutureProvider.autoDispose<List<ProductModel>>((ref) async {
      const params = CustomerProductsParams(page: 1, perPage: 100);
      final products = await ref.watch(customerProductsProvider(params).future);
      final list = products.where((p) => p.isAvailable).toList();
      list.shuffle();
      return list.take(15).toList();
    });

// Customer actions provider
final customerActionsProvider = Provider((ref) => CustomerActions(ref));

class CustomerActions {
  final Ref ref;

  CustomerActions(this.ref);

  Future<OrderModel> createOrder(Map<String, dynamic> orderData) async {
    final customerService = ref.read(customerApiServiceProvider);
    final loading = ref.read(loadingProvider.notifier);

    return loading.withLoading(LoadingKeys.createOrder, () async {
      final response = await customerService.createOrder(orderData);

      if (response.success && response.data != null) {
        // Invalidate orders cache
        ref.invalidate(customerOrdersProvider);
        return OrderModel.fromJson(response.data);
      } else {
        throw Exception(response.message);
      }
    });
  }

  Future<void> cancelOrder(String orderId, String reason) async {
    final customerService = ref.read(customerApiServiceProvider);
    final loading = ref.read(loadingProvider.notifier);

    return loading.withLoading(LoadingKeys.cancelOrder, () async {
      final response = await customerService.cancelOrder(orderId, {
        'reason': reason,
      });

      if (!response.success) {
        throw Exception(response.message);
      }

      // Invalidate orders cache
      ref.invalidate(customerOrdersProvider);
    });
  }

  Future<void> rateOrder(
    String orderId,
    Map<String, dynamic> ratingData,
  ) async {
    final customerService = ref.read(customerApiServiceProvider);
    final loading = ref.read(loadingProvider.notifier);

    return loading.withLoading('rate_order', () async {
      final response = await customerService.rateOrder(orderId, ratingData);

      if (!response.success) {
        throw Exception(response.message);
      }

      // Invalidate orders cache
      ref.invalidate(customerOrdersProvider);
    });
  }
}

// Parameter classes
class CustomerShopsParams extends Equatable {
  final int? page;
  final int? perPage;
  final String? search;
  final int? categoryId;

  const CustomerShopsParams({
    this.page,
    this.perPage,
    this.search,
    this.categoryId,
  });

  @override
  List<Object?> get props => [page, perPage, search, categoryId];
}

class CustomerProductsParams extends Equatable {
  final int? page;
  final int? perPage;
  final String? search;

  const CustomerProductsParams({this.page, this.perPage, this.search});

  @override
  List<Object?> get props => [page, perPage, search];
}

class CustomerOrdersParams {
  final int? page;
  final int? perPage;
  final String? status;

  const CustomerOrdersParams({this.page, this.perPage, this.status});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CustomerOrdersParams &&
          runtimeType == other.runtimeType &&
          page == other.page &&
          perPage == other.perPage &&
          status == other.status;

  @override
  int get hashCode => page.hashCode ^ perPage.hashCode ^ status.hashCode;
}

class ShopProductsParams extends Equatable {
  final String shopId;
  final int? page;
  final int? perPage;

  const ShopProductsParams({required this.shopId, this.page, this.perPage});

  @override
  List<Object?> get props => [shopId, page, perPage];
}
