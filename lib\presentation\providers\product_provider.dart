import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/models/product_model.dart';
import '../../data/models/shop_model.dart';
import '../../data/repositories/product_repository_impl.dart';
import '../../domain/usecases/product_usecases.dart';
import 'auth_provider.dart';

// Repository Provider
final productRepositoryProvider = Provider<ProductRepositoryImpl>((ref) {
  return ProductRepositoryImpl(
    localDataSource: ref.read(localDataSourceProvider),
  );
});

// Use Cases Providers
final getProductsUseCaseProvider = Provider<GetProductsUseCase>((ref) {
  return GetProductsUseCase(ref.read(productRepositoryProvider));
});

final getProductByIdUseCaseProvider = Provider<GetProductByIdUseCase>((ref) {
  return GetProductByIdUseCase(ref.read(productRepositoryProvider));
});

final getProductsByShopUseCaseProvider = Provider<GetProductsByShopUseCase>((
  ref,
) {
  return GetProductsByShopUseCase(ref.read(productRepositoryProvider));
});

final searchProductsUseCaseProvider = Provider<SearchProductsUseCase>((ref) {
  return SearchProductsUseCase(ref.read(productRepositoryProvider));
});

final getCategoriesUseCaseProvider = Provider<GetCategoriesUseCase>((ref) {
  return GetCategoriesUseCase(ref.read(productRepositoryProvider));
});

// State Providers
final productsProvider = FutureProvider<List<ProductModel>>((ref) async {
  final useCase = ref.read(getProductsUseCaseProvider);
  final result = await useCase();
  return result.fold(
    (failure) => throw Exception(failure.message),
    (products) => products,
  );
});

final categoriesProvider = FutureProvider<List<CategoryModel>>((ref) async {
  final useCase = ref.read(getCategoriesUseCaseProvider);
  final result = await useCase();
  return result.fold(
    (failure) => throw Exception(failure.message),
    (categories) => categories,
  );
});

final productsByShopProvider =
    FutureProvider.family<List<ProductModel>, String>((ref, shopId) async {
      final useCase = ref.read(getProductsByShopUseCaseProvider);
      final result = await useCase(shopId);
      return result.fold(
        (failure) => throw Exception(failure.message),
        (products) => products,
      );
    });

final productByIdProvider = FutureProvider.family<ProductModel?, String>((
  ref,
  productId,
) async {
  final useCase = ref.read(getProductByIdUseCaseProvider);
  final result = await useCase(productId);
  return result.fold(
    (failure) => throw Exception(failure.message),
    (product) => product,
  );
});

// Popular products provider (simulated based on price and availability)
final popularProductsProvider = FutureProvider<List<ProductModel>>((ref) async {
  final products = await ref.watch(productsProvider.future);

  // Filter available products and simulate popularity
  final popular = products.where((product) => product.isAvailable).toList();

  // Sort by price (lower price = more popular for demo)
  popular.sort((a, b) => a.price.compareTo(b.price));

  // Return top 10 popular products
  return popular.take(10).toList();
});

// Best selling products provider (simulated)
final bestSellingProductsProvider = FutureProvider<List<ProductModel>>((
  ref,
) async {
  final products = await ref.watch(productsProvider.future);

  // Filter available products
  final bestSelling = products.where((product) => product.isAvailable).toList();

  // Simulate best selling by shuffling and taking random products
  bestSelling.shuffle();

  // Return top 15 best selling products
  return bestSelling.take(15).toList();
});

// Featured products provider (high-priced items as premium)
final featuredProductsProvider = FutureProvider<List<ProductModel>>((
  ref,
) async {
  final products = await ref.watch(productsProvider.future);

  // Filter available products with higher prices (premium items)
  final featured = products
      .where(
        (product) => product.isAvailable && product.price >= 50000,
      ) // Products above 50k VND
      .toList();

  // Sort by price descending (most expensive first)
  featured.sort((a, b) => b.price.compareTo(a.price));

  // Return top 8 featured products
  return featured.take(8).toList();
});

// Products by category provider
final productsByCategoryProvider =
    FutureProvider.family<List<ProductModel>, String>((
      ref,
      categoryName,
    ) async {
      final products = await ref.watch(productsProvider.future);

      return products
          .where(
            (product) =>
                product.categoryId?.toString() == categoryName &&
                product.isAvailable,
          )
          .toList();
    });

// Search State
class SearchState {
  final List<ProductModel> products;
  final bool isLoading;
  final String? error;
  final String query;

  const SearchState({
    this.products = const [],
    this.isLoading = false,
    this.error,
    this.query = '',
  });

  SearchState copyWith({
    List<ProductModel>? products,
    bool? isLoading,
    String? error,
    String? query,
  }) {
    return SearchState(
      products: products ?? this.products,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      query: query ?? this.query,
    );
  }
}

class SearchNotifier extends StateNotifier<SearchState> {
  final SearchProductsUseCase searchProductsUseCase;

  SearchNotifier({required this.searchProductsUseCase})
    : super(const SearchState());

  Future<void> searchProducts(String query) async {
    if (query.trim().isEmpty) {
      state = state.copyWith(products: [], query: query);
      return;
    }

    state = state.copyWith(isLoading: true, error: null, query: query);

    final result = await searchProductsUseCase(query);

    result.fold(
      (failure) {
        state = state.copyWith(isLoading: false, error: failure.message);
      },
      (products) {
        state = state.copyWith(
          products: products,
          isLoading: false,
          error: null,
        );
      },
    );
  }

  void clearSearch() {
    state = const SearchState();
  }
}

final searchProvider = StateNotifierProvider<SearchNotifier, SearchState>((
  ref,
) {
  return SearchNotifier(
    searchProductsUseCase: ref.read(searchProductsUseCaseProvider),
  );
});
