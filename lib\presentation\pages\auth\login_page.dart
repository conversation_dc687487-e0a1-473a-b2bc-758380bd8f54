import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../providers/auth_provider.dart';
import '../../../core/constants/app_colors.dart';
import '../../widgets/app_logo.dart';
import '../../../core/constants/app_constants.dart';
import '../../widgets/loading_overlay.dart';
import '../../widgets/error_dialog.dart';
import '../../../core/utils/error_handler.dart';

class LoginPage extends ConsumerStatefulWidget {
  const LoginPage({super.key});

  @override
  ConsumerState<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends ConsumerState<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;

  // Demo accounts for different roles
  final List<DemoAccount> _demoAccounts = [
    DemoAccount(
      '<EMAIL>',
      'password123',
      'Khách hàng',
      AppConstants.roleCustomer,
    ),
    DemoAccount(
      '<EMAIL>',
      'password123',
      'Shop',
      AppConstants.roleShop,
    ),
    DemoAccount(
      '<EMAIL>',
      'password123',
      'Shipper',
      AppConstants.roleShipper,
    ),
  ];

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);

    // Listen to auth state changes
    ref.listen<AuthState>(authProvider, (previous, next) {
      if (next.isAuthenticated && next.user != null) {
        final role = next.user!.role;
        String route;
        switch (role) {
          case AppConstants.roleShop:
            route = '/shop';
            break;
          case AppConstants.roleShipper:
            route = '/shipper';
            break;
          case AppConstants.roleCustomer:
          default:
            route = '/customer';
            break;
        }
        SnackBarUtils.showSuccess(context, 'Đăng nhập thành công!');
        context.go(route);
      } else if (next.error != null && previous?.error != next.error) {
        // Show error when there's a new error
        SnackBarUtils.showError(context, next.error!);
      }
    });

    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: 40), // Add some top spacing
                // Logo and Title
                const AppLogo.custom(size: 80, borderRadius: 16),
                const SizedBox(height: 24),
                Text(
                  'Shipper19+',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary, // Chữ trắng
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  'Đăng nhập để tiếp tục',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 48),

                // Email Field
                TextFormField(
                  controller: _emailController,
                  keyboardType: TextInputType.emailAddress,
                  decoration: const InputDecoration(
                    labelText: 'Email',
                    prefixIcon: Icon(Icons.email),
                    border: OutlineInputBorder(),
                  ),
                  validator: ValidationUtils.validateEmail,
                ),
                const SizedBox(height: 16),

                // Password Field
                TextFormField(
                  controller: _passwordController,
                  obscureText: _obscurePassword,
                  decoration: InputDecoration(
                    labelText: 'Mật khẩu',
                    prefixIcon: const Icon(Icons.lock),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _obscurePassword
                            ? Icons.visibility
                            : Icons.visibility_off,
                      ),
                      onPressed: () {
                        setState(() {
                          _obscurePassword = !_obscurePassword;
                        });
                      },
                    ),
                    border: const OutlineInputBorder(),
                  ),
                  validator: ValidationUtils.validatePassword,
                ),
                const SizedBox(height: 24),

                // Error Message
                if (authState.error != null)
                  Container(
                    padding: const EdgeInsets.all(12),
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      color: Colors.red[50],
                      border: Border.all(color: Colors.red[200]!),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      authState.error!,
                      style: TextStyle(color: Colors.red[700]),
                    ),
                  ),

                // Login Button
                LoadingButton(
                  isLoading: authState.isLoading,
                  onPressed: _handleLogin,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'Đăng nhập',
                    style: TextStyle(fontSize: 16),
                  ),
                ),
                const SizedBox(height: 16),

                // Forgot Password Link
                Align(
                  alignment: Alignment.centerRight,
                  child: TextButton(
                    onPressed: () => context.go('/forgot-password'),
                    child: const Text('Quên mật khẩu?'),
                  ),
                ),
                const SizedBox(height: 8),

                // Phone Login Link
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text('Hoặc đăng nhập bằng '),
                    TextButton(
                      onPressed: () => context.go('/phone-login'),
                      child: const Text('Số điện thoại'),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Register Link
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text('Chưa có tài khoản? '),
                    TextButton(
                      onPressed: () => context.go('/register'),
                      child: const Text('Đăng ký ngay'),
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // Demo Accounts
                ExpansionTile(
                  title: Text(
                    'Tài khoản demo',
                    style: Theme.of(context).textTheme.titleSmall,
                  ),
                  children: [
                    const SizedBox(height: 16),
                    ...(_demoAccounts.map(
                      (account) => Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        child: OutlinedButton(
                          onPressed: () => _loginWithDemo(account),
                          child: Text('${account.label} (${account.email})'),
                        ),
                      ),
                    )),
                  ],
                ),
                const SizedBox(height: 40), // Add bottom spacing
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _handleLogin() async {
    if (_formKey.currentState == null || !_formKey.currentState!.validate()) {
      return;
    }

    final email = _emailController.text.trim();
    final password = _passwordController.text;

    // Use AuthProvider instead of direct HTTP call
    await ref.read(authProvider.notifier).login(email, password);
  }

  void _loginWithDemo(DemoAccount account) {
    _emailController.text = account.email;
    _passwordController.text = account.password;
    _handleLogin();
  }
}

class DemoAccount {
  final String email;
  final String password;
  final String label;
  final String role;

  DemoAccount(this.email, this.password, this.label, this.role);
}
