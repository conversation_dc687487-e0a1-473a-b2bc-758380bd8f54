import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'shop_model.g.dart';

@JsonSerializable()
class ShopModel extends Equatable {
  @Json<PERSON>ey(fromJson: _intFromAny)
  final int id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_id', fromJson: _nullableIntFromAny)
  final int? userId;
  final String name;
  final String? description;
  final String? address;
  final String? contact;
  final String? phone;
  final String? email;
  final String? image;
  final List<String>? images;
  @Json<PERSON>ey(fromJson: _nullableDoubleFromString)
  final double? latitude;
  @JsonKey(fromJson: _nullableDoubleFromString)
  final double? longitude;
  @J<PERSON><PERSON><PERSON>(name: 'is_active', fromJson: _boolFromAny)
  final bool isActive;
  @Json<PERSON>ey(fromJson: _nullableDoubleFromString)
  final double? rating;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'review_count', fromJson: _nullableIntFromAny)
  final int? reviewCount;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'business_hours')
  final String? businessHours;
  @J<PERSON><PERSON><PERSON>(name: 'is_open', fromJson: _nullableBoolFromAny)
  final bool? isOpen;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'estimated_delivery_time', fromJson: _nullableIntFromAny)
  final int? estimatedDeliveryTime;
  @JsonKey(name: 'delivery_fee', fromJson: _nullableDoubleFromString)
  final double? deliveryFee;
  @JsonKey(name: 'minimum_order', fromJson: _nullableDoubleFromString)
  final double? minimumOrder;
  @JsonKey(name: 'category_ids', fromJson: _intListFromAny)
  final List<int>? categoryIds;
  @JsonKey(name: 'created_at')
  final DateTime? createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime? updatedAt;

  const ShopModel({
    required this.id,
    this.userId,
    required this.name,
    this.description,
    this.address,
    this.contact,
    this.phone,
    this.email,
    this.image,
    this.images,
    this.latitude,
    this.longitude,
    required this.isActive,
    this.rating,
    this.reviewCount,
    this.businessHours,
    this.isOpen,
    this.estimatedDeliveryTime,
    this.deliveryFee,
    this.minimumOrder,
    this.categoryIds,
    this.createdAt,
    this.updatedAt,
  });

  factory ShopModel.fromJson(Map<String, dynamic> json) =>
      _$ShopModelFromJson(json);

  Map<String, dynamic> toJson() => _$ShopModelToJson(this);

  @override
  List<Object?> get props => [
    id,
    userId,
    name,
    description,
    address,
    contact,
    phone,
    email,
    image,
    images,
    latitude,
    longitude,
    isActive,
    rating,
    reviewCount,
    businessHours,
    isOpen,
    estimatedDeliveryTime,
    deliveryFee,
    minimumOrder,
    categoryIds,
    createdAt,
    updatedAt,
  ];
}

@JsonSerializable()
class CategoryModel extends Equatable {
  @JsonKey(fromJson: _intFromAny)
  final int id;
  final String name;
  final String? description;
  final String? icon;

  const CategoryModel({
    required this.id,
    required this.name,
    this.description,
    this.icon,
  });

  factory CategoryModel.fromJson(Map<String, dynamic> json) =>
      _$CategoryModelFromJson(json);

  Map<String, dynamic> toJson() => _$CategoryModelToJson(this);

  @override
  List<Object?> get props => [id, name, description, icon];
}

@JsonSerializable()
class ShopStatsModel extends Equatable {
  @JsonKey(name: 'total_orders')
  final int totalOrders;
  @JsonKey(name: 'total_revenue', fromJson: _doubleFromString)
  final double totalRevenue;
  @JsonKey(name: 'completed_orders')
  final int completedOrders;
  @JsonKey(name: 'pending_orders')
  final int pendingOrders;
  @JsonKey(name: 'cancelled_orders')
  final int cancelledOrders;
  @JsonKey(name: 'average_rating', fromJson: _doubleFromString)
  final double averageRating;
  @JsonKey(name: 'daily_stats')
  final List<DailyStatsModel>? dailyStats;

  const ShopStatsModel({
    required this.totalOrders,
    required this.totalRevenue,
    required this.completedOrders,
    required this.pendingOrders,
    required this.cancelledOrders,
    required this.averageRating,
    this.dailyStats,
  });

  factory ShopStatsModel.fromJson(Map<String, dynamic> json) =>
      _$ShopStatsModelFromJson(json);

  Map<String, dynamic> toJson() => _$ShopStatsModelToJson(this);

  @override
  List<Object?> get props => [
    totalOrders,
    totalRevenue,
    completedOrders,
    pendingOrders,
    cancelledOrders,
    averageRating,
    dailyStats,
  ];
}

@JsonSerializable()
class DailyStatsModel extends Equatable {
  final String date;
  final int orders;
  @JsonKey(fromJson: _doubleFromString)
  final double revenue;

  const DailyStatsModel({
    required this.date,
    required this.orders,
    required this.revenue,
  });

  factory DailyStatsModel.fromJson(Map<String, dynamic> json) =>
      _$DailyStatsModelFromJson(json);

  Map<String, dynamic> toJson() => _$DailyStatsModelToJson(this);

  @override
  List<Object?> get props => [date, orders, revenue];
}

// Helper functions for JSON conversion
double _doubleFromString(dynamic value) {
  if (value == null) return 0.0;
  if (value is double) return value;
  if (value is int) return value.toDouble();
  if (value is String) return double.tryParse(value) ?? 0.0;
  return 0.0;
}

double? _nullableDoubleFromString(dynamic value) {
  if (value == null) return null;
  if (value is double) return value;
  if (value is int) return value.toDouble();
  if (value is String) return double.tryParse(value);
  return null;
}

// Robust helpers for mixed-type API fields
int _intFromAny(dynamic value) {
  if (value == null) return 0;
  if (value is int) return value;
  if (value is double) return value.toInt();
  if (value is String) {
    final i = int.tryParse(value);
    if (i != null) return i;
    final d = double.tryParse(value);
    if (d != null) return d.toInt();
  }
  return 0;
}

int? _nullableIntFromAny(dynamic value) {
  if (value == null) return null;
  return _intFromAny(value);
}

bool _boolFromAny(dynamic value) {
  if (value is bool) return value;
  if (value is num) return value != 0;
  if (value is String) return value == '1' || value.toLowerCase() == 'true';
  return false;
}

bool? _nullableBoolFromAny(dynamic value) {
  if (value == null) return null;
  return _boolFromAny(value);
}

List<int>? _intListFromAny(dynamic value) {
  if (value == null) return null;
  if (value is List) {
    final result = <int>[];
    for (final e in value) {
      final v = _nullableIntFromAny(e);
      if (v != null) result.add(v);
    }
    return result;
  }
  return null;
}
