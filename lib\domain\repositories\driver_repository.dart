import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../data/models/order_model.dart';
import '../../data/models/driver_stats_model.dart';

abstract class DriverRepository {
  Future<Either<Failure, List<OrderModel>>> getAvailableOrders();
  Future<Either<Failure, OrderModel>> getOrderDetail(String orderId);
  Future<Either<Failure, void>> acceptOrder(String orderId);
  Future<Either<Failure, void>> updateOrderStatus(String orderId, String status);
  Future<Either<Failure, DriverStatsModel>> getDriverStats(String? date);
}
