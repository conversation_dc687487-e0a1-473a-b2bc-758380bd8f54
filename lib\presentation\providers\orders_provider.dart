import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/models/order_model.dart';
import '../../domain/usecases/usecase_providers.dart';
import '../../domain/usecases/driver_usecases.dart';

// Orders State
class OrdersState {
  final List<OrderModel> availableOrders;
  final List<OrderModel> myOrders;
  final OrderModel? selectedOrder;
  final bool isLoading;
  final String? error;

  const OrdersState({
    this.availableOrders = const [],
    this.myOrders = const [],
    this.selectedOrder,
    this.isLoading = false,
    this.error,
  });

  OrdersState copyWith({
    List<OrderModel>? availableOrders,
    List<OrderModel>? myOrders,
    OrderModel? selectedOrder,
    bool? isLoading,
    String? error,
  }) {
    return OrdersState(
      availableOrders: availableOrders ?? this.availableOrders,
      myOrders: myOrders ?? this.myOrders,
      selectedOrder: selectedOrder ?? this.selectedOrder,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// Orders Notifier
class OrdersNotifier extends StateNotifier<OrdersState> {
  final GetAvailableOrdersUseCase getAvailableOrdersUseCase;
  final GetOrderDetailUseCase getOrderDetailUseCase;
  final AcceptOrderUseCase acceptOrderUseCase;
  final UpdateOrderStatusUseCase updateOrderStatusUseCase;

  OrdersNotifier({
    required this.getAvailableOrdersUseCase,
    required this.getOrderDetailUseCase,
    required this.acceptOrderUseCase,
    required this.updateOrderStatusUseCase,
  }) : super(const OrdersState());

  Future<void> loadAvailableOrders() async {
    state = state.copyWith(isLoading: true, error: null);

    final result = await getAvailableOrdersUseCase();

    result.fold(
      (failure) {
        state = state.copyWith(isLoading: false, error: failure.message);
      },
      (orders) {
        state = state.copyWith(
          availableOrders: orders,
          isLoading: false,
          error: null,
        );
      },
    );
  }

  Future<void> loadOrderDetail(String orderId) async {
    state = state.copyWith(isLoading: true, error: null);

    final result = await getOrderDetailUseCase(orderId);

    result.fold(
      (failure) {
        state = state.copyWith(isLoading: false, error: failure.message);
      },
      (order) {
        state = state.copyWith(
          selectedOrder: order,
          isLoading: false,
          error: null,
        );
      },
    );
  }

  Future<bool> acceptOrder(String orderId) async {
    state = state.copyWith(isLoading: true, error: null);

    final result = await acceptOrderUseCase(orderId);

    return result.fold(
      (failure) {
        state = state.copyWith(isLoading: false, error: failure.message);
        return false;
      },
      (_) {
        state = state.copyWith(isLoading: false, error: null);
        // Refresh available orders
        loadAvailableOrders();
        return true;
      },
    );
  }

  Future<bool> updateOrderStatus(String orderId, String status) async {
    state = state.copyWith(isLoading: true, error: null);

    final result = await updateOrderStatusUseCase(orderId, status);

    return result.fold(
      (failure) {
        state = state.copyWith(isLoading: false, error: failure.message);
        return false;
      },
      (_) {
        state = state.copyWith(isLoading: false, error: null);
        // Refresh order detail
        if (state.selectedOrder?.id.toString() == orderId) {
          loadOrderDetail(orderId);
        }
        return true;
      },
    );
  }

  void clearSelectedOrder() {
    state = state.copyWith(selectedOrder: null);
  }
}

// Orders Provider
final ordersProvider = StateNotifierProvider<OrdersNotifier, OrdersState>((
  ref,
) {
  return OrdersNotifier(
    getAvailableOrdersUseCase: ref.read(getAvailableOrdersUseCaseProvider),
    getOrderDetailUseCase: ref.read(getOrderDetailUseCaseProvider),
    acceptOrderUseCase: ref.read(acceptOrderUseCaseProvider),
    updateOrderStatusUseCase: ref.read(updateOrderStatusUseCaseProvider),
  );
});
