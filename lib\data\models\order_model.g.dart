// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AddressModel _$AddressModelFromJson(Map<String, dynamic> json) => AddressModel(
      address: json['address'] as String,
      latitude: _doubleFromString(json['latitude']),
      longitude: _doubleFromString(json['longitude']),
      contactName: json['contact_name'] as String,
      contactPhone: json['contact_phone'] as String,
      note: json['note'] as String?,
    );

Map<String, dynamic> _$AddressModelToJson(AddressModel instance) =>
    <String, dynamic>{
      'address': instance.address,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'contact_name': instance.contactName,
      'contact_phone': instance.contactPhone,
      'note': instance.note,
    };

FeeBreakdownModel _$FeeBreakdownModelFromJson(Map<String, dynamic> json) =>
    FeeBreakdownModel(
      subtotal: _doubleFromString(json['subtotal']),
      deliveryFee: _doubleFromString(json['delivery_fee']),
      serviceFee: _doubleFromString(json['service_fee']),
      discount: _doubleFromString(json['discount']),
      total: _doubleFromString(json['total']),
      surcharges: json['surcharges'] as List<dynamic>?,
    );

Map<String, dynamic> _$FeeBreakdownModelToJson(FeeBreakdownModel instance) =>
    <String, dynamic>{
      'subtotal': instance.subtotal,
      'delivery_fee': instance.deliveryFee,
      'service_fee': instance.serviceFee,
      'discount': instance.discount,
      'total': instance.total,
      'surcharges': instance.surcharges,
    };

TimelineModel _$TimelineModelFromJson(Map<String, dynamic> json) =>
    TimelineModel(
      status: json['status'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      note: json['note'] as String,
      updatedBy: json['updated_by'] as String?,
    );

Map<String, dynamic> _$TimelineModelToJson(TimelineModel instance) =>
    <String, dynamic>{
      'status': instance.status,
      'timestamp': instance.timestamp.toIso8601String(),
      'note': instance.note,
      'updated_by': instance.updatedBy,
    };

ReceiverInfoModel _$ReceiverInfoModelFromJson(Map<String, dynamic> json) =>
    ReceiverInfoModel(
      name: json['name'] as String,
      phone: json['phone'] as String,
      email: json['email'] as String?,
    );

Map<String, dynamic> _$ReceiverInfoModelToJson(ReceiverInfoModel instance) =>
    <String, dynamic>{
      'name': instance.name,
      'phone': instance.phone,
      'email': instance.email,
    };

OrderItemModel _$OrderItemModelFromJson(Map<String, dynamic> json) =>
    OrderItemModel(
      productId: (json['product_id'] as num).toInt(),
      productName: json['product_name'] as String,
      quantity: (json['quantity'] as num).toInt(),
      unitPrice: _doubleFromString(json['unit_price']),
      totalPrice: _doubleFromString(json['total_price']),
      selectedVariant: json['selected_variant'] as String?,
      selectedToppings: (json['selected_toppings'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      note: json['note'] as String?,
    );

Map<String, dynamic> _$OrderItemModelToJson(OrderItemModel instance) =>
    <String, dynamic>{
      'product_id': instance.productId,
      'product_name': instance.productName,
      'quantity': instance.quantity,
      'unit_price': instance.unitPrice,
      'total_price': instance.totalPrice,
      'selected_variant': instance.selectedVariant,
      'selected_toppings': instance.selectedToppings,
      'note': instance.note,
    };

OrderModel _$OrderModelFromJson(Map<String, dynamic> json) => OrderModel(
      id: (json['id'] as num).toInt(),
      customerId: (json['customer_id'] as num?)?.toInt(),
      shopId: (json['shop_id'] as num?)?.toInt(),
      driverId: (json['driver_id'] as num?)?.toInt(),
      items: (json['items'] as List<dynamic>)
          .map((e) => OrderItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      pickupAddress:
          AddressModel.fromJson(json['pickup_address'] as Map<String, dynamic>),
      dropoffAddress: AddressModel.fromJson(
          json['dropoff_address'] as Map<String, dynamic>),
      feeBreakdown: FeeBreakdownModel.fromJson(
          json['fee_breakdown'] as Map<String, dynamic>),
      status: json['status'] as String,
      note: json['note'] as String?,
      paymentMethod: json['payment_method'] as String?,
      paymentStatus: json['payment_status'] as String?,
      timeline: (json['timeline'] as List<dynamic>)
          .map((e) => TimelineModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      receiverInfo: json['receiver_info'] == null
          ? null
          : ReceiverInfoModel.fromJson(
              json['receiver_info'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      shop: json['shop'] == null
          ? null
          : OrderShopModel.fromJson(json['shop'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$OrderModelToJson(OrderModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'customer_id': instance.customerId,
      'shop_id': instance.shopId,
      'driver_id': instance.driverId,
      'items': instance.items,
      'pickup_address': instance.pickupAddress,
      'dropoff_address': instance.dropoffAddress,
      'fee_breakdown': instance.feeBreakdown,
      'status': instance.status,
      'note': instance.note,
      'payment_method': instance.paymentMethod,
      'payment_status': instance.paymentStatus,
      'timeline': instance.timeline,
      'receiver_info': instance.receiverInfo,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'shop': instance.shop,
    };

OrderShopModel _$OrderShopModelFromJson(Map<String, dynamic> json) =>
    OrderShopModel(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      address: json['address'] as String,
      phone: json['phone'] as String?,
      email: json['email'] as String?,
      rating: _nullableDoubleFromString(json['rating']),
      reviewCount: _nullableIntFromAny(json['review_count']),
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$OrderShopModelToJson(OrderShopModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'address': instance.address,
      'phone': instance.phone,
      'email': instance.email,
      'rating': instance.rating,
      'review_count': instance.reviewCount,
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
    };
