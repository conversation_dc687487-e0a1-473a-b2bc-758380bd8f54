import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class LoadingList extends StatelessWidget {
  final int itemCount;
  final double itemHeight;
  final EdgeInsets? padding;

  const LoadingList({
    super.key,
    this.itemCount = 5,
    this.itemHeight = 80,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: padding,
      itemCount: itemCount,
      itemBuilder: (context, index) => Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: ShimmerItem(height: itemHeight),
      ),
    );
  }
}

class ShimmerItem extends StatelessWidget {
  final double height;
  final double? width;
  final BorderRadius? borderRadius;

  const ShimmerItem({
    super.key,
    required this.height,
    this.width,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: borderRadius ?? BorderRadius.circular(8),
        ),
      ),
    );
  }
}

class ShopCardSkeleton extends StatelessWidget {
  const ShopCardSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const ShimmerItem(height: 60, width: 60),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const ShimmerItem(height: 16, width: 120),
                      const SizedBox(height: 8),
                      const ShimmerItem(height: 12, width: 80),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          const ShimmerItem(height: 12, width: 60),
                          const SizedBox(width: 8),
                          const ShimmerItem(height: 12, width: 40),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const ShimmerItem(height: 12, width: double.infinity),
            const SizedBox(height: 4),
            const ShimmerItem(height: 12, width: 200),
          ],
        ),
      ),
    );
  }
}

class ProductCardSkeleton extends StatelessWidget {
  const ProductCardSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const ShimmerItem(height: 120, width: double.infinity),
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const ShimmerItem(height: 16, width: 100),
                const SizedBox(height: 8),
                const ShimmerItem(height: 12, width: 80),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const ShimmerItem(height: 14, width: 60),
                    const ShimmerItem(height: 24, width: 40),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class OrderCardSkeleton extends StatelessWidget {
  const OrderCardSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const ShimmerItem(height: 16, width: 80),
                const ShimmerItem(height: 20, width: 60),
              ],
            ),
            const SizedBox(height: 12),
            const ShimmerItem(height: 14, width: 120),
            const SizedBox(height: 8),
            const ShimmerItem(height: 12, width: 200),
            const SizedBox(height: 8),
            const ShimmerItem(height: 12, width: 150),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const ShimmerItem(height: 12, width: 80),
                const ShimmerItem(height: 16, width: 60),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class LoadingGrid extends StatelessWidget {
  final int itemCount;
  final int crossAxisCount;
  final Widget Function(int index) itemBuilder;
  final EdgeInsets? padding;

  const LoadingGrid({
    super.key,
    this.itemCount = 6,
    this.crossAxisCount = 2,
    required this.itemBuilder,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      padding: padding,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: 0.8,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: itemCount,
      itemBuilder: (context, index) => itemBuilder(index),
    );
  }
}

// Utility widget for conditional loading
class ConditionalLoading extends StatelessWidget {
  final bool isLoading;
  final Widget loadingWidget;
  final Widget child;

  const ConditionalLoading({
    super.key,
    required this.isLoading,
    required this.loadingWidget,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return isLoading ? loadingWidget : child;
  }
}

// Pull to refresh with loading
class LoadingRefreshIndicator extends StatelessWidget {
  final Widget child;
  final Future<void> Function() onRefresh;
  final bool isLoading;

  const LoadingRefreshIndicator({
    super.key,
    required this.child,
    required this.onRefresh,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: isLoading ? () async {} : onRefresh,
      child: child,
    );
  }
}
