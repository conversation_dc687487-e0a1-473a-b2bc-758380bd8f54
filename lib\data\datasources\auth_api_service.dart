import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import '../models/api_response_model.dart';

part 'auth_api_service.g.dart';

@RestApi()
abstract class AuthApiService {
  factory AuthApiService(Dio dio, {String baseUrl}) = _AuthApiService;

  @POST('/register')
  Future<BaseApiResponseModel> register(
    @Body() Map<String, dynamic> registerData,
  );

  @POST('/login')
  Future<BaseApiResponseModel> login(@Body() Map<String, dynamic> loginData);

  @POST('/logout')
  Future<SimpleApiResponseModel> logout();

  @GET('/me')
  Future<BaseApiResponseModel> getCurrentUser();

  // OTP Login
  @POST('/login/send-otp')
  Future<SimpleApiResponseModel> sendOtp(@Body() Map<String, dynamic> body);

  @POST('/login/verify-otp')
  Future<BaseApiResponseModel> verifyOtp(@Body() Map<String, dynamic> body);

  // User Location
  @POST('/users/location')
  Future<SimpleApiResponseModel> updateLocation(
    @Body() Map<String, dynamic> body,
  );

  @GET('/users/{id}/location')
  Future<BaseApiResponseModel> getUserLocation(@Path('id') String userId);
}
