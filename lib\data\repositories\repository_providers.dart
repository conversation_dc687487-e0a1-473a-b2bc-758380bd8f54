import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/repositories/user_repository.dart';
import '../../domain/repositories/driver_repository.dart';
import '../datasources/api_service_provider.dart';
import 'user_repository_impl.dart';
import 'driver_repository_impl.dart';

// User Repository Provider
final userRepositoryProvider = Provider<UserRepository>((ref) {
  final authApiService = ref.read(authApiServiceProvider);
  return UserRepositoryImpl(authApiService: authApiService);
});

// Driver Repository Provider
final driverRepositoryProvider = Provider<DriverRepository>((ref) {
  final driverApiService = ref.read(driverApiServiceProvider);
  return DriverRepositoryImpl(driverApiService: driverApiService);
});
