import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../data/models/product_model.dart';
import '../../data/models/shop_model.dart';

abstract class ProductRepository {
  Future<Either<Failure, List<ProductModel>>> getProducts();
  Future<Either<Failure, ProductModel?>> getProductById(String id);
  Future<Either<Failure, List<ProductModel>>> getProductsByShop(String shopId);
  Future<Either<Failure, List<ProductModel>>> getProductsByCategory(
    String categoryId,
  );
  Future<Either<Failure, List<ProductModel>>> searchProducts(String query);
  Future<Either<Failure, List<CategoryModel>>> getCategories();
}
