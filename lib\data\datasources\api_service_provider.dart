import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dio/dio.dart';
import '../../core/network/dio_config.dart';
import 'auth_api_service.dart';
import 'driver_api_service.dart';
import 'customer_api_service.dart';
import 'shop_api_service.dart';
import 'admin_api_service.dart';

// Dio provider
final dioProvider = Provider<Dio>((ref) {
  return DioConfig.createDio();
});

// Auth API Service provider
final authApiServiceProvider = Provider<AuthApiService>((ref) {
  final dio = ref.read(dioProvider);
  return AuthApiService(dio);
});

// Driver API Service provider
final driverApiServiceProvider = Provider<DriverApiService>((ref) {
  final dio = ref.read(dioProvider);
  return DriverApiService(dio);
});

// Customer API Service provider
final customerApiServiceProvider = Provider<CustomerApiService>((ref) {
  final dio = ref.read(dioProvider);
  return CustomerApiService(dio);
});

// Shop API Service provider
final shopApiServiceProvider = Provider<ShopApiService>((ref) {
  final dio = ref.read(dioProvider);
  return ShopApiService(dio);
});

// Admin API Service provider
final adminApiServiceProvider = Provider<AdminApiService>((ref) {
  final dio = ref.read(dioProvider);
  return AdminApiService(dio);
});
