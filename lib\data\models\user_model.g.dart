// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserProfileModel _$UserProfileModelFromJson(Map<String, dynamic> json) =>
    UserProfileModel(
      address: json['address'] as String?,
      latitude: _nullableDoubleFromString(json['latitude']),
      longitude: _nullableDoubleFromString(json['longitude']),
      dateOfBirth: json['date_of_birth'] as String?,
      gender: json['gender'] as String?,
      shopProfile: json['shop_profile'] == null
          ? null
          : ShopProfileModel.fromJson(
              json['shop_profile'] as Map<String, dynamic>),
      shipperProfile: json['shipper_profile'] == null
          ? null
          : ShipperProfileModel.fromJson(
              json['shipper_profile'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$UserProfileModelToJson(UserProfileModel instance) =>
    <String, dynamic>{
      'address': instance.address,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'date_of_birth': instance.dateOfBirth,
      'gender': instance.gender,
      'shop_profile': instance.shopProfile,
      'shipper_profile': instance.shipperProfile,
    };

ShopProfileModel _$ShopProfileModelFromJson(Map<String, dynamic> json) =>
    ShopProfileModel(
      businessName: json['business_name'] as String?,
      businessAddress: json['business_address'] as String?,
      businessPhone: json['business_phone'] as String?,
      businessEmail: json['business_email'] as String?,
      businessLatitude: (json['business_latitude'] as num?)?.toDouble(),
      businessLongitude: (json['business_longitude'] as num?)?.toDouble(),
      businessHours: json['business_hours'] as String?,
      isVerified: json['is_verified'] as bool?,
    );

Map<String, dynamic> _$ShopProfileModelToJson(ShopProfileModel instance) =>
    <String, dynamic>{
      'business_name': instance.businessName,
      'business_address': instance.businessAddress,
      'business_phone': instance.businessPhone,
      'business_email': instance.businessEmail,
      'business_latitude': instance.businessLatitude,
      'business_longitude': instance.businessLongitude,
      'business_hours': instance.businessHours,
      'is_verified': instance.isVerified,
    };

ShipperProfileModel _$ShipperProfileModelFromJson(Map<String, dynamic> json) =>
    ShipperProfileModel(
      vehicleType: json['vehicle_type'] as String?,
      licensePlate: json['license_plate'] as String?,
      rating: (json['rating'] as num?)?.toDouble(),
      totalOrders: (json['total_orders'] as num?)?.toInt(),
      activeOrderCount: (json['active_order_count'] as num?)?.toInt(),
      isOnline: json['is_online'] as bool?,
      currentLocation: json['current_location'] as String?,
    );

Map<String, dynamic> _$ShipperProfileModelToJson(
        ShipperProfileModel instance) =>
    <String, dynamic>{
      'vehicle_type': instance.vehicleType,
      'license_plate': instance.licensePlate,
      'rating': instance.rating,
      'total_orders': instance.totalOrders,
      'active_order_count': instance.activeOrderCount,
      'is_online': instance.isOnline,
      'current_location': instance.currentLocation,
    };

UserModel _$UserModelFromJson(Map<String, dynamic> json) => UserModel(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String?,
      role: json['role'] as String,
      avatar: json['avatar'] as String?,
      isActive: json['is_active'] as bool?,
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
      driver: json['driver'] == null
          ? null
          : DriverModel.fromJson(json['driver'] as Map<String, dynamic>),
      profile: json['profile'] == null
          ? null
          : UserProfileModel.fromJson(json['profile'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$UserModelToJson(UserModel instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'phone': instance.phone,
      'role': instance.role,
      'avatar': instance.avatar,
      'is_active': instance.isActive,
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
      'driver': instance.driver,
      'profile': instance.profile,
    };
