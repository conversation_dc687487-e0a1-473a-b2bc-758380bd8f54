import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import 'user_model.dart';

part 'auth_response_model.g.dart';

@JsonSerializable()
class AuthResponseModel extends Equatable {
  final UserModel user;
  final String token;
  @Json<PERSON>ey(name: 'token_type')
  final String tokenType;

  const AuthResponseModel({
    required this.user,
    required this.token,
    required this.tokenType,
  });

  factory AuthResponseModel.fromJson(Map<String, dynamic> json) => _$AuthResponseModelFromJson(json);
  Map<String, dynamic> toJson() => _$AuthResponseModelToJson(this);

  @override
  List<Object?> get props => [user, token, tokenType];
}
