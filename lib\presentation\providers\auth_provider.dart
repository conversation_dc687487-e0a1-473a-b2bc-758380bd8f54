import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../core/constants/app_constants.dart';
import '../../data/models/user_model.dart';
import '../../data/models/auth_response_model.dart';
import '../../domain/usecases/usecase_providers.dart';
import '../../domain/usecases/auth_usecases.dart';
import '../../data/datasources/local_data_source.dart';

// Local Data Source Provider
final localDataSourceProvider = Provider<LocalDataSource>((ref) {
  return LocalDataSourceImpl();
});

// Auth State
class AuthState {
  final UserModel? user;
  final bool isLoading;
  final String? error;
  final bool isAuthenticated;

  const AuthState({
    this.user,
    this.isLoading = false,
    this.error,
    this.isAuthenticated = false,
  });

  AuthState copyWith({
    UserModel? user,
    bool? isLoading,
    String? error,
    bool? isAuthenticated,
  }) {
    return AuthState(
      user: user ?? this.user,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
    );
  }
}

// Auth Notifier
class AuthNotifier extends StateNotifier<AuthState> {
  final LoginUseCase loginUseCase;
  final LogoutUseCase logoutUseCase;
  final GetCurrentUserUseCase getCurrentUserUseCase;

  AuthNotifier({
    required this.loginUseCase,
    required this.logoutUseCase,
    required this.getCurrentUserUseCase,
  }) : super(const AuthState()) {
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString(AppConstants.userTokenKey);

    if (token != null && token.isNotEmpty) {
      // Validate token with server by getting current user
      final result = await getCurrentUserUseCase();
      result.fold(
        (failure) {
          // Token is invalid, clear it
          prefs.clear();
          state = const AuthState();
        },
        (user) {
          state = state.copyWith(user: user, isAuthenticated: true);
        },
      );
    }
  }

  Future<void> login(String email, String password) async {
    state = state.copyWith(isLoading: true, error: null);

    final result = await loginUseCase(email, password);

    result.fold(
      (failure) {
        state = state.copyWith(
          isLoading: false,
          error: failure.message,
          isAuthenticated: false,
        );
      },
      (authResponse) async {
        await _saveAuthData(authResponse);
        state = state.copyWith(
          user: authResponse.user,
          isLoading: false,
          error: null,
          isAuthenticated: true,
        );
      },
    );
  }

  Future<void> logout() async {
    try {
      // Call API logout
      await logoutUseCase();
    } catch (e) {
      // Continue with logout even if API call fails
      print('Logout API call failed: $e');
    }

    // Clear local storage
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();

    // Reset state
    state = const AuthState();
  }

  Future<void> register(UserModel user) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // For now, simulate registration success
      // In a real app, this would call a registration API
      await Future.delayed(const Duration(seconds: 1));

      // Simulate successful registration by logging in the user
      state = state.copyWith(
        user: user,
        isLoading: false,
        error: null,
        isAuthenticated: true,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Registration failed: ${e.toString()}',
        isAuthenticated: false,
      );
    }
  }

  Future<void> _saveAuthData(AuthResponseModel authResponse) async {
    final prefs = await SharedPreferences.getInstance();

    // Safe null checks and type casting
    if (authResponse.token.isNotEmpty) {
      await prefs.setString(AppConstants.userTokenKey, authResponse.token);
    }

    // user.id is required (int) so always exists
    await prefs.setString(
      AppConstants.userIdKey,
      authResponse.user.id.toString(),
    );

    if (authResponse.user.role.isNotEmpty) {
      await prefs.setString(AppConstants.userRoleKey, authResponse.user.role);
    }
  }
}

// Auth Provider
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier(
    loginUseCase: ref.read(loginUseCaseProvider),
    logoutUseCase: ref.read(logoutUseCaseProvider),
    getCurrentUserUseCase: ref.read(getCurrentUserUseCaseProvider),
  );
});
