// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProductModel _$ProductModelFromJson(Map<String, dynamic> json) => ProductModel(
      id: (json['id'] as num).toInt(),
      shopId: (json['shop_id'] as num?)?.toInt(),
      name: json['name'] as String,
      description: json['description'] as String?,
      price: _doubleFromString(json['price']),
      status: json['status'] as String?,
      image: json['image'] as String?,
      images:
          (json['images'] as List<dynamic>?)?.map((e) => e as String).toList(),
      categoryId: (json['category_id'] as num?)?.toInt(),
      isAvailable: json['is_available'] as bool,
      variants: (json['variants'] as List<dynamic>?)
          ?.map((e) => ProductVariantModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      toppings: (json['toppings'] as List<dynamic>?)
          ?.map((e) => ProductToppingModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      rejectionReason: json['rejection_reason'] as String?,
      reviewedAt: json['reviewed_at'] == null
          ? null
          : DateTime.parse(json['reviewed_at'] as String),
      reviewedBy: (json['reviewed_by'] as num?)?.toInt(),
      shop: json['shop'] == null
          ? null
          : ShopModel.fromJson(json['shop'] as Map<String, dynamic>),
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$ProductModelToJson(ProductModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'shop_id': instance.shopId,
      'name': instance.name,
      'description': instance.description,
      'price': instance.price,
      'status': instance.status,
      'image': instance.image,
      'images': instance.images,
      'category_id': instance.categoryId,
      'is_available': instance.isAvailable,
      'variants': instance.variants,
      'toppings': instance.toppings,
      'rejection_reason': instance.rejectionReason,
      'reviewed_at': instance.reviewedAt?.toIso8601String(),
      'reviewed_by': instance.reviewedBy,
      'shop': instance.shop,
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
    };

ProductVariantModel _$ProductVariantModelFromJson(Map<String, dynamic> json) =>
    ProductVariantModel(
      id: json['id'] as String,
      name: json['name'] as String,
      additionalPrice: _doubleFromString(json['additional_price']),
      isRequired: json['is_required'] as bool,
    );

Map<String, dynamic> _$ProductVariantModelToJson(
        ProductVariantModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'additional_price': instance.additionalPrice,
      'is_required': instance.isRequired,
    };

ProductToppingModel _$ProductToppingModelFromJson(Map<String, dynamic> json) =>
    ProductToppingModel(
      id: json['id'] as String,
      name: json['name'] as String,
      price: _doubleFromString(json['price']),
      isAvailable: json['is_available'] as bool,
    );

Map<String, dynamic> _$ProductToppingModelToJson(
        ProductToppingModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'price': instance.price,
      'is_available': instance.isAvailable,
    };

ProductCategoryModel _$ProductCategoryModelFromJson(
        Map<String, dynamic> json) =>
    ProductCategoryModel(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
    );

Map<String, dynamic> _$ProductCategoryModelToJson(
        ProductCategoryModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
    };
