import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/repositories/repository_providers.dart';
import 'auth_usecases.dart';
import 'driver_usecases.dart';

// Auth Use Cases
final loginUseCaseProvider = Provider<LoginUseCase>((ref) {
  return LoginUseCase(ref.read(userRepositoryProvider));
});

final logoutUseCaseProvider = Provider<LogoutUseCase>((ref) {
  return LogoutUseCase(ref.read(userRepositoryProvider));
});

final getCurrentUserUseCaseProvider = Provider<GetCurrentUserUseCase>((ref) {
  return GetCurrentUserUseCase(ref.read(userRepositoryProvider));
});

// Driver Use Cases
final getAvailableOrdersUseCaseProvider = Provider<GetAvailableOrdersUseCase>((ref) {
  return GetAvailableOrdersUseCase(ref.read(driverRepositoryProvider));
});

final getOrderDetailUseCaseProvider = Provider<GetOrderDetailUseCase>((ref) {
  return GetOrderDetailUseCase(ref.read(driverRepositoryProvider));
});

final acceptOrderUseCaseProvider = Provider<AcceptOrderUseCase>((ref) {
  return AcceptOrderUseCase(ref.read(driverRepositoryProvider));
});

final updateOrderStatusUseCaseProvider = Provider<UpdateOrderStatusUseCase>((ref) {
  return UpdateOrderStatusUseCase(ref.read(driverRepositoryProvider));
});

final getDriverStatsUseCaseProvider = Provider<GetDriverStatsUseCase>((ref) {
  return GetDriverStatsUseCase(ref.read(driverRepositoryProvider));
});
