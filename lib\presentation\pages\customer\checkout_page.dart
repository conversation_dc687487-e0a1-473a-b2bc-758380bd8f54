import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../providers/cart_provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/location_provider.dart';
import '../../../data/datasources/api_service_provider.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';

class CheckoutPage extends ConsumerStatefulWidget {
  const CheckoutPage({super.key});

  @override
  ConsumerState<CheckoutPage> createState() => _CheckoutPageState();
}

class _CheckoutPageState extends ConsumerState<CheckoutPage> {
  String _selectedPaymentMethod = AppConstants.paymentCash;
  String _selectedAddress = '';
  final _noteController = TextEditingController();
  bool _isLoading = false;

  // Sample addresses (in real app, this would come from user profile)
  final List<Map<String, dynamic>> _addresses = [
    {
      'id': '1',
      'label': 'Nhà',
      'address': '123 Nguyễn Văn Cừ, Quận 5, TP.HCM',
      'phone': '**********',
      'isDefault': true,
    },
    {
      'id': '2',
      'label': 'Công ty',
      'address': '456 Lê Văn Sỹ, Quận 3, TP.HCM',
      'phone': '**********',
      'isDefault': false,
    },
  ];

  @override
  void initState() {
    super.initState();
    // Set default address
    final defaultAddress = _addresses.firstWhere(
      (addr) => addr['isDefault'] == true,
      orElse: () => _addresses.first,
    );
    _selectedAddress = defaultAddress['id'];
  }

  @override
  void dispose() {
    _noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final cartState = ref.watch(cartProvider);
    ref.watch(authProvider);

    if (cartState.isEmpty) {
      return Scaffold(
        appBar: AppBar(title: const Text('Thanh toán')),
        body: const Center(child: Text('Giỏ hàng trống')),
      );
    }

    return Scaffold(
      appBar: AppBar(title: const Text('Thanh toán'), elevation: 0),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Delivery Address Section
            _buildAddressSection(),

            // Order Summary Section
            _buildOrderSummarySection(cartState),

            // Payment Method Section
            _buildPaymentMethodSection(),

            // Note Section
            _buildNoteSection(),

            // Order Total Section
            _buildOrderTotalSection(cartState),
          ],
        ),
      ),
      bottomNavigationBar: _buildPlaceOrderButton(cartState),
    );
  }

  Widget _buildAddressSection() {
    final selectedAddr = _addresses.firstWhere(
      (addr) => addr['id'] == _selectedAddress,
    );

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.location_on, color: AppColors.primary),
              const SizedBox(width: 8),
              const Text(
                'Địa chỉ giao hàng',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              TextButton(
                onPressed: _showAddressSelection,
                child: const Text('Thay đổi'),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        selectedAddr['label'],
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      selectedAddr['phone'],
                      style: const TextStyle(fontWeight: FontWeight.w600),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  selectedAddr['address'],
                  style: TextStyle(color: Colors.grey[700]),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderSummarySection(CartState cartState) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.restaurant, color: AppColors.primary),
              const SizedBox(width: 8),
              Text(
                cartState.restaurantName ?? 'Nhà hàng',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...cartState.items.map((item) => _buildOrderItem(item)),
        ],
      ),
    );
  }

  Widget _buildOrderItem(CartItem item) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Center(
              child: Text(
                '${item.quantity}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.product.name,
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
                if (item.selectedVariant != null)
                  Text(
                    'Size: ${item.selectedVariant!.name}',
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                if (item.selectedToppings.isNotEmpty)
                  Text(
                    'Topping: ${item.selectedToppings.map((t) => t.name).join(', ')}',
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                if (item.note != null && item.note!.isNotEmpty)
                  Text(
                    'Ghi chú: ${item.note}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
              ],
            ),
          ),
          Text(
            '${item.totalPrice.toStringAsFixed(0)}đ',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethodSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.payment, color: AppColors.primary),
              const SizedBox(width: 8),
              const Text(
                'Phương thức thanh toán',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildPaymentOption(
            AppConstants.paymentCash,
            'Thanh toán khi nhận hàng (COD)',
            Icons.money,
            'Thanh toán bằng tiền mặt khi nhận hàng',
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentOption(
    String value,
    String title,
    IconData icon,
    String subtitle,
  ) {
    return RadioListTile<String>(
      value: value,
      groupValue: _selectedPaymentMethod,
      onChanged: (String? newValue) {
        setState(() {
          _selectedPaymentMethod = newValue!;
        });
      },
      title: Row(
        children: [Icon(icon, size: 20), const SizedBox(width: 8), Text(title)],
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(color: Colors.grey[600], fontSize: 12),
      ),
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildNoteSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.note, color: AppColors.primary),
              const SizedBox(width: 8),
              const Text(
                'Ghi chú cho đơn hàng',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 12),
          TextField(
            controller: _noteController,
            maxLines: 3,
            decoration: InputDecoration(
              hintText: 'Ghi chú cho shipper (tùy chọn)',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.all(12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderTotalSection(CartState cartState) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildTotalRow(
            'Tạm tính',
            '${cartState.subtotal.toStringAsFixed(0)}đ',
          ),
          _buildTotalRow(
            'Phí giao hàng',
            '${cartState.deliveryFee.toStringAsFixed(0)}đ',
          ),
          _buildTotalRow(
            'Phí dịch vụ',
            '${cartState.serviceFee.toStringAsFixed(0)}đ',
          ),
          if (cartState.discount > 0)
            _buildTotalRow(
              'Giảm giá',
              '-${cartState.discount.toStringAsFixed(0)}đ',
              color: Colors.green,
            ),
          const Divider(),
          _buildTotalRow(
            'Tổng cộng',
            '${cartState.total.toStringAsFixed(0)}đ',
            isTotal: true,
          ),
        ],
      ),
    );
  }

  Widget _buildTotalRow(
    String label,
    String value, {
    bool isTotal = false,
    Color? color,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: color,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: color ?? (isTotal ? AppColors.primary : null),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlaceOrderButton(CartState cartState) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: ElevatedButton(
        onPressed: _isLoading ? null : () => _placeOrder(cartState),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                'Đặt hàng • ${cartState.total.toStringAsFixed(0)}đ',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
      ),
    );
  }

  void _showAddressSelection() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Chọn địa chỉ giao hàng',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ..._addresses.map(
              (addr) => ListTile(
                leading: Radio<String>(
                  value: addr['id'],
                  groupValue: _selectedAddress,
                  onChanged: (value) {
                    setState(() {
                      _selectedAddress = value!;
                    });
                    Navigator.pop(context);
                  },
                ),
                title: Text(addr['label']),
                subtitle: Text(addr['address']),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () {
                  Navigator.pop(context);
                  // Navigate to add address page
                },
                icon: const Icon(Icons.add),
                label: const Text('Thêm địa chỉ mới'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _placeOrder(CartState cartState) async {
    setState(() => _isLoading = true);

    try {
      // Chỉ lưu phương thức thanh toán gửi lên server (không xử lý sâu)

      // Build payload theo tài liệu API (mở rộng theo doc)
      final items = cartState.items.map((ci) {
        final unitPrice = (ci.totalPrice / ci.quantity);
        return {
          'product_id': ci.product.id,
          'product_name': ci.product.name,
          'quantity': ci.quantity,
          'unit_price': unitPrice,
          'total_price': ci.totalPrice,
          if (ci.selectedVariant != null)
            'selected_variant': ci.selectedVariant!.name,
          if (ci.selectedToppings.isNotEmpty)
            'selected_toppings': ci.selectedToppings
                .map((t) => t.name)
                .toList(),
          if ((ci.note ?? '').isNotEmpty) 'note': ci.note,
        };
      }).toList();

      // Lấy shopId: ưu tiên từ cart.restaurantId, fallback product.shopId
      int? shopId = int.tryParse(cartState.restaurantId ?? '');
      shopId ??= cartState.items.isNotEmpty
          ? cartState.items.first.product.shopId
          : null;
      if (shopId == null) {
        throw Exception('Không xác định được cửa hàng của giỏ hàng');
      }

      // Pick-up từ shop trong item đầu (nếu có), fallback giá trị mặc định
      final firstShop = cartState.items.isNotEmpty
          ? cartState.items.first.product.shop
          : null;
      final pickupAddress = {
        'address': firstShop?.address ?? 'Cửa hàng',
        'latitude': (firstShop?.latitude ?? 10.8231),
        'longitude': (firstShop?.longitude ?? 106.6297),
        'contact_name': firstShop?.name ?? 'Cửa hàng',
        'contact_phone': (firstShop?.phone ?? firstShop?.contact ?? ''),
      };

      // Drop-off từ địa chỉ được chọn + vị trí hiện tại
      final selectedAddr = _addresses.firstWhere(
        (a) => a['id'] == _selectedAddress,
      );
      final locState = ref.read(locationProvider);
      final dropoffAddress = {
        'address': selectedAddr['address'] as String,
        'latitude': locState.latitude,
        'longitude': locState.longitude,
        'contact_name': selectedAddr['label'] as String,
        'contact_phone': selectedAddr['phone'] as String,
      };

      // Map phương thức thanh toán về API values
      String paymentMethod;
      switch (_selectedPaymentMethod) {
        case AppConstants.paymentCash:
          paymentMethod = 'COD'; // Server expects COD for cash on delivery
          break;
        case AppConstants.paymentBankTransfer:
          paymentMethod = 'BANK_TRANSFER';
          break;
        case AppConstants.paymentEWallet:
          paymentMethod = 'E_WALLET';
          break;
        default:
          paymentMethod = 'COD';
      }

      final auth = ref.read(authProvider);
      final feeBreakdown = {
        'subtotal': cartState.subtotal,
        'delivery_fee': cartState.deliveryFee,
        'service_fee': cartState.serviceFee,
        'discount': cartState.discount,
        'total': cartState.total,
        'surcharges': <dynamic>[],
      };
      final receiverInfo = {
        'name': selectedAddr['label'] as String,
        'phone': selectedAddr['phone'] as String,
        if (auth.user?.email != null) 'email': auth.user!.email,
      };

      final body = {
        if (auth.user != null) 'customer_id': auth.user!.id,
        'shop_id': shopId,
        'items': items,
        'pickup_address': pickupAddress,
        'dropoff_address': dropoffAddress,
        'fee_breakdown': feeBreakdown,
        'status': 'NEW',
        'payment_method': paymentMethod,
        'payment_status': 'UNPAID',
        if (_noteController.text.trim().isNotEmpty)
          'note': _noteController.text.trim(),
        'receiver_info': receiverInfo,
      };

      final api = ref.read(customerApiServiceProvider);
      final res = await api.createOrder(body);

      if (res.success) {
        // Lấy orderId và chuyển trang tracking
        final orderId = (res.data['id'] ?? res.data['order_id'] ?? '')
            .toString();
        ref.read(cartProvider.notifier).clearCart();
        if (mounted) {
          if (orderId.isNotEmpty) {
            context.goNamed(
              'order-tracking',
              pathParameters: {'orderId': orderId},
            );
          } else {
            context.go('/order-success');
          }
        }
      } else {
        throw Exception(res.message);
      }
    } catch (e) {
      if (mounted) {
        String msg = 'Đặt hàng thất bại';
        if (e is DioException) {
          final data = e.response?.data;
          if (data is Map && data['message'] is String) msg = data['message'];
          if (data is Map &&
              data['errors'] is Map &&
              (data['errors'] as Map).isNotEmpty) {
            final first = (data['errors'] as Map).values.first;
            if (first is List && first.isNotEmpty) msg = first.first.toString();
          }
        } else {
          msg = e.toString();
        }
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(msg), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  /* Payment SDK stubs removed intentionally to satisfy lints */
  /*
  Future<bool> _processMoMoPayment(double amount) async {
    // Simulate MoMo payment processing
    await Future.delayed(const Duration(seconds: 1));

    // In real app, integrate with MoMo SDK
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Thanh toán MoMo'),
            content: Text('Thanh toán ${amount.toStringAsFixed(0)}đ qua MoMo?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Hủy'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Thanh toán'),
              ),
            ],
          ),
        ) ??
        false;
  }
  */
}
