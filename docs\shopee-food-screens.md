## Cây luồng màn hình chi tiết (Mobile) + screen_id

Lưu ý: Đ<PERSON><PERSON> là đề xuất tổng quát để tham chiếu thiết kế/route/analytics. <PERSON><PERSON> thể điều chỉnh theo framework/router.


### 0) Cây màn hình theo loại tài khoản

Shop
- Đăng nhập → Trang chủ Shop → Tạo đơn → Chọn sản phẩm → Nhập địa chỉ gửi/nhận → <PERSON><PERSON><PERSON>h<PERSON>n → <PERSON>õ<PERSON> đ<PERSON> → <PERSON><PERSON><PERSON> sử → Thống kê
- Quản lý sản phẩm: <PERSON><PERSON> sách → Tạo mới/Sửa/Xo<PERSON> → <PERSON><PERSON><PERSON> c<PERSON><PERSON> duy<PERSON> → (Admin duyệt) → Công khai/ Huỷ đăng
- Bài viết từ Admin: Danh sách bài viết → Chi tiết

Shipper
- Đ<PERSON>ng nhập → Trang chủ Shipper → Danh sách đơn mới (realtime) → Chi tiết đơn → N<PERSON>ậ<PERSON> đơn (n<PERSON><PERSON> còn) → <PERSON><PERSON> lấy hàng → <PERSON><PERSON> giao → <PERSON><PERSON><PERSON> thành → Thống kê cá nhân
- Giới hạn: tối đa 3 đơn đồng thời; đơn đã nhận ẩn khỏi danh sách người khác

Khách hàng
- Đăng nhập/đặt nhanh → Trang chủ KH → Danh sách sản phẩm & Tìm kiếm → Chi tiết sản phẩm → Đặt đơn → Theo dõi → Lịch sử

Admin
- Đăng nhập → Duyệt sản phẩm → Bài viết → Thống kê → Reassign đơn

### 1) Cây luồng màn hình (ASCII)

App Root
├── Onboarding / Đăng nhập
│   ├── Đăng nhập SĐT (OTP)
│   └── Đăng nhập Email/Password
├── Quyền vị trí (Permission)
├── Trang chủ (Home)
│   ├── Tìm kiếm (Search)
│   │   ├── Kết quả tìm kiếm (Results)
│   │   │   ├── Bộ lọc (Filters)
│   │   │   └── Sắp xếp (Sort)
│   │   └── Không có kết quả (Empty State)
│   ├── Danh mục (Categories)
│   │   └── DS nhà hàng theo danh mục (Restaurant Listing)
│   ├── Flash Sale / Ưu đãi
│   └── Nhà hàng nổi bật / Gần bạn
├── Trang Nhà hàng (Restaurant Detail)
│   ├── Tabs
│   │   ├── Menu
│   │   │   ├── Nhóm món (Sections)
│   │   │   └── Chi tiết món (Bottom Sheet / Page)
│   │   ├── Review
│   │   └── Info (Thông tin)
│   └── Trạng thái mở cửa / ETA / Phí giao
├── Chi tiết món (Item Detail)
│   ├── Biến thể/Size
│   ├── Topping (checkbox)
│   ├── Ghi chú
│   └── [Thêm vào giỏ] (validation)
├── Giỏ hàng (Cart)
│   ├── Danh sách item (Sửa/Xoá/Số lượng)
│   ├── Áp voucher
│   ├── Phí giao / Phí nền tảng
│   └── [Tiếp tục đặt hàng]
├── Checkout
│   ├── Địa chỉ giao (Address)
│   │   ├── Danh sách địa chỉ (List)
│   │   └── Thêm/Sửa địa chỉ (Map + Form)
│   ├── Phương thức thanh toán (Payment Method)
│   │   ├── COD
│   │   ├── Ví điện tử
│   │   └── Thẻ (3DS)
│   ├── Xem lại đơn (Review Order)
│   └── [Đặt hàng]
├── Trạng thái đơn (Order Tracking)
│   ├── Bản đồ (Vị trí tài xế)
│   ├── Mốc trạng thái (Timeline)
│   ├── Chat/Call
│   └── Hủy đơn (nếu còn cho phép)
├── Đánh giá (Rate & Review)
│   └── Rating + Nhận xét + Ảnh
├── Ví/Ưu đãi (Wallet & Vouchers)
│   ├── Ví/Điểm (nếu có)
│   └── Voucher của tôi
├── Tài khoản (Account)
│   ├── Hồ sơ
│   ├── Địa chỉ
│   ├── Lịch sử đơn
│   │   └── Chi tiết đơn
│   └── Cài đặt
└── Hỗ trợ (Support)
    ├── FAQ
    └── Tạo yêu cầu hỗ trợ (Ticket)

Nhánh/luồng lỗi thường gặp
- Thanh toán thất bại: Checkout > Payment Method > (Retry/Đổi phương thức).
- Không có tài xế: Sau Đặt hàng > Thông báo chờ > Hủy tự động/Nhận bồi thường (nếu có).
- Món bán hết: Item Detail > Gợi ý thay thế > Cập nhật giỏ.

### 2) Bảng screen_id và screen name đề xuất

Quy ước:
- screen_id: UPPER_SNAKE_CASE (ổn định, dùng trong analytics/log/router constants)
- Screen Name (UI/Code): PascalCase (tên component/screen hiển thị trong code)

Danh sách chính (Mobile):
- ONBOARDING → Onboarding
- AUTH_LOGIN_OTP → LoginOTP
- AUTH_LOGIN_EMAIL → LoginEmail
- PERMISSION_LOCATION → LocationPermission
- HOME → Home
- SEARCH → Search
- SEARCH_RESULTS → SearchResults
- SEARCH_FILTERS → SearchFilters
- SEARCH_SORT → SearchSort
- CATEGORY_LIST → CategoryRestaurants
- RESTAURANT_DETAIL → RestaurantDetail
- RESTAURANT_MENU_SECTION → MenuSection
- ITEM_DETAIL → ItemDetail
- CART → Cart
- CHECKOUT_ADDRESS_LIST → AddressList
- CHECKOUT_ADDRESS_FORM → AddressForm
- CHECKOUT_PAYMENT → PaymentMethod
- CHECKOUT_REVIEW → ReviewOrder
- ORDER_TRACKING → OrderTracking
- RATE_REVIEW → RateAndReview
- WALLET → Wallet
- VOUCHERS → MyVouchers
- ACCOUNT_PROFILE → AccountProfile
- ACCOUNT_ADDRESSES → AccountAddresses
- ACCOUNT_ORDER_HISTORY → OrderHistory
- ACCOUNT_ORDER_DETAIL → OrderDetail
- ACCOUNT_SETTINGS → AccountSettings
- SUPPORT_FAQ → SupportFAQ
- SUPPORT_TICKET → SupportTicket

### 3) Sơ đồ Mermaid (tree/graph)

```mermaid
flowchart TD
  AppRoot[App Root]
  AppRoot --> ONBOARDING[Onboarding (ONBOARDING)]
  ONBOARDING --> AUTH_LOGIN_OTP[LoginOTP (AUTH_LOGIN_OTP)]
  ONBOARDING --> AUTH_LOGIN_EMAIL[LoginEmail (AUTH_LOGIN_EMAIL)]
  AppRoot --> PERMISSION_LOCATION[LocationPermission (PERMISSION_LOCATION)]
  AppRoot --> HOME[Home (HOME)]

  HOME --> SEARCH[Search (SEARCH)]
  SEARCH --> SEARCH_RESULTS[SearchResults (SEARCH_RESULTS)]
  SEARCH_RESULTS --> SEARCH_FILTERS[SearchFilters (SEARCH_FILTERS)]
  SEARCH_RESULTS --> SEARCH_SORT[SearchSort (SEARCH_SORT)]
  HOME --> CATEGORY_LIST[CategoryRestaurants (CATEGORY_LIST)]
  HOME --> RESTAURANT_DETAIL[RestaurantDetail (RESTAURANT_DETAIL)]

  RESTAURANT_DETAIL --> RESTAURANT_MENU_SECTION[MenuSection (RESTAURANT_MENU_SECTION)]
  RESTAURANT_DETAIL --> ITEM_DETAIL[ItemDetail (ITEM_DETAIL)]
  ITEM_DETAIL --> CART[Cart (CART)]
  CART --> CHECKOUT_ADDRESS_LIST[AddressList (CHECKOUT_ADDRESS_LIST)]
  CHECKOUT_ADDRESS_LIST --> CHECKOUT_ADDRESS_FORM[AddressForm (CHECKOUT_ADDRESS_FORM)]
  CHECKOUT_ADDRESS_LIST --> CHECKOUT_PAYMENT[PaymentMethod (CHECKOUT_PAYMENT)]
  CHECKOUT_PAYMENT --> CHECKOUT_REVIEW[ReviewOrder (CHECKOUT_REVIEW)]
  CHECKOUT_REVIEW --> ORDER_TRACKING[OrderTracking (ORDER_TRACKING)]

  ORDER_TRACKING --> RATE_REVIEW[RateAndReview (RATE_REVIEW)]

  HOME --> WALLET[Wallet (WALLET)]
  WALLET --> VOUCHERS[MyVouchers (VOUCHERS)]
  HOME --> ACCOUNT_PROFILE[AccountProfile (ACCOUNT_PROFILE)]
  ACCOUNT_PROFILE --> ACCOUNT_ADDRESSES[AccountAddresses (ACCOUNT_ADDRESSES)]
  ACCOUNT_PROFILE --> ACCOUNT_ORDER_HISTORY[OrderHistory (ACCOUNT_ORDER_HISTORY)]
  ACCOUNT_ORDER_HISTORY --> ACCOUNT_ORDER_DETAIL[OrderDetail (ACCOUNT_ORDER_DETAIL)]
  ACCOUNT_PROFILE --> ACCOUNT_SETTINGS[AccountSettings (ACCOUNT_SETTINGS)]
  HOME --> SUPPORT_FAQ[SupportFAQ (SUPPORT_FAQ)]
  SUPPORT_FAQ --> SUPPORT_TICKET[SupportTicket (SUPPORT_TICKET)]
```

