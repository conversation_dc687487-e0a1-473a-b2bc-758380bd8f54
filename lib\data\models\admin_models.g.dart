// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PostModel _$PostModelFromJson(Map<String, dynamic> json) => PostModel(
      id: (json['id'] as num).toInt(),
      title: json['title'] as String,
      content: json['content'] as String,
      audience: json['audience'] as String,
      createdBy: (json['created_by'] as num).toInt(),
      isPublished: json['is_published'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$PostModelToJson(PostModel instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'content': instance.content,
      'audience': instance.audience,
      'created_by': instance.createdBy,
      'is_published': instance.isPublished,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };

PricingConfigModel _$PricingConfigModelFromJson(Map<String, dynamic> json) =>
    PricingConfigModel(
      basePrice: (json['base_price'] as num).toDouble(),
      pricePerKm: (json['price_per_km'] as num).toDouble(),
      minPrice: (json['min_price'] as num).toDouble(),
      maxPrice: (json['max_price'] as num).toDouble(),
    );

Map<String, dynamic> _$PricingConfigModelToJson(PricingConfigModel instance) =>
    <String, dynamic>{
      'base_price': instance.basePrice,
      'price_per_km': instance.pricePerKm,
      'min_price': instance.minPrice,
      'max_price': instance.maxPrice,
    };

SurchargeModel _$SurchargeModelFromJson(Map<String, dynamic> json) =>
    SurchargeModel(
      name: json['name'] as String,
      type: json['type'] as String,
      value: (json['value'] as num).toDouble(),
      isActive: json['is_active'] as bool,
    );

Map<String, dynamic> _$SurchargeModelToJson(SurchargeModel instance) =>
    <String, dynamic>{
      'name': instance.name,
      'type': instance.type,
      'value': instance.value,
      'is_active': instance.isActive,
    };

AdminStatsSummaryModel _$AdminStatsSummaryModelFromJson(
        Map<String, dynamic> json) =>
    AdminStatsSummaryModel(
      totalUsers: (json['total_users'] as num).toInt(),
      totalShops: (json['total_shops'] as num).toInt(),
      totalDrivers: (json['total_drivers'] as num).toInt(),
      totalOrders: (json['total_orders'] as num).toInt(),
      totalRevenue: (json['total_revenue'] as num).toDouble(),
      pendingOrders: (json['pending_orders'] as num).toInt(),
      completedOrders: (json['completed_orders'] as num).toInt(),
      cancelledOrders: (json['cancelled_orders'] as num).toInt(),
    );

Map<String, dynamic> _$AdminStatsSummaryModelToJson(
        AdminStatsSummaryModel instance) =>
    <String, dynamic>{
      'total_users': instance.totalUsers,
      'total_shops': instance.totalShops,
      'total_drivers': instance.totalDrivers,
      'total_orders': instance.totalOrders,
      'total_revenue': instance.totalRevenue,
      'pending_orders': instance.pendingOrders,
      'completed_orders': instance.completedOrders,
      'cancelled_orders': instance.cancelledOrders,
    };

ShopStatsItemModel _$ShopStatsItemModelFromJson(Map<String, dynamic> json) =>
    ShopStatsItemModel(
      shopId: (json['shop_id'] as num).toInt(),
      shopName: json['shop_name'] as String,
      totalOrders: (json['total_orders'] as num).toInt(),
      totalRevenue: (json['total_revenue'] as num).toDouble(),
      averageOrderValue: (json['average_order_value'] as num).toDouble(),
    );

Map<String, dynamic> _$ShopStatsItemModelToJson(ShopStatsItemModel instance) =>
    <String, dynamic>{
      'shop_id': instance.shopId,
      'shop_name': instance.shopName,
      'total_orders': instance.totalOrders,
      'total_revenue': instance.totalRevenue,
      'average_order_value': instance.averageOrderValue,
    };

RevenueStatsModel _$RevenueStatsModelFromJson(Map<String, dynamic> json) =>
    RevenueStatsModel(
      date: json['date'] as String,
      totalRevenue: (json['total_revenue'] as num).toDouble(),
      totalOrders: (json['total_orders'] as num).toInt(),
      averageOrderValue: (json['average_order_value'] as num).toDouble(),
    );

Map<String, dynamic> _$RevenueStatsModelToJson(RevenueStatsModel instance) =>
    <String, dynamic>{
      'date': instance.date,
      'total_revenue': instance.totalRevenue,
      'total_orders': instance.totalOrders,
      'average_order_value': instance.averageOrderValue,
    };
