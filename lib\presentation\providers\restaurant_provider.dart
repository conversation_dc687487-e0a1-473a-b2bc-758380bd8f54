import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/models/restaurant_model.dart';
import '../../data/repositories/restaurant_repository_impl.dart';
import 'auth_provider.dart';
import 'location_provider.dart';

// Repository Provider
final restaurantRepositoryProvider = Provider<RestaurantRepositoryImpl>((ref) {
  return RestaurantRepositoryImpl(
    localDataSource: ref.read(localDataSourceProvider),
  );
});

// State Providers
final restaurantsProvider = FutureProvider<List<RestaurantModel>>((ref) async {
  final repository = ref.read(restaurantRepositoryProvider);
  final result = await repository.getRestaurants();
  return result.fold(
    (failure) => throw Exception(failure.message),
    (restaurants) => restaurants,
  );
});

final restaurantByIdProvider = FutureProvider.family<RestaurantModel?, String>((
  ref,
  restaurantId,
) async {
  final repository = ref.read(restaurantRepositoryProvider);
  final result = await repository.getRestaurantById(restaurantId);
  return result.fold(
    (failure) => throw Exception(failure.message),
    (restaurant) => restaurant,
  );
});

final vouchersProvider = FutureProvider<List<VoucherModel>>((ref) async {
  final repository = ref.read(restaurantRepositoryProvider);
  final result = await repository.getVouchers();
  return result.fold(
    (failure) => throw Exception(failure.message),
    (vouchers) => vouchers,
  );
});

final postsProvider = FutureProvider<List<PostModel>>((ref) async {
  final repository = ref.read(restaurantRepositoryProvider);
  final result = await repository.getPosts();
  return result.fold(
    (failure) => throw Exception(failure.message),
    (posts) => posts,
  );
});

// Featured restaurants provider (high rating and active)
final featuredRestaurantsProvider = FutureProvider<List<RestaurantModel>>((
  ref,
) async {
  final restaurants = await ref.watch(restaurantsProvider.future);

  // Filter and sort featured restaurants
  final featured = restaurants
      .where(
        (restaurant) =>
            restaurant.isActive &&
            restaurant.isOpen &&
            restaurant.rating >= 4.0,
      )
      .toList();

  // Sort by rating descending
  featured.sort((a, b) => b.rating.compareTo(a.rating));

  // Return top 10 featured restaurants
  return featured.take(10).toList();
});

// Nearby restaurants provider (requires location)
final nearbyRestaurantsProvider = FutureProvider<List<RestaurantModel>>((
  ref,
) async {
  final restaurants = await ref.watch(restaurantsProvider.future);
  final locationState = ref.watch(locationProvider);

  if (!locationState.hasLocation) {
    return restaurants.where((r) => r.isActive && r.isOpen).toList();
  }

  // Calculate distance and sort
  final restaurantsWithDistance = restaurants
      .where((restaurant) => restaurant.isActive && restaurant.isOpen)
      .map((restaurant) {
        final distance = ref
            .read(locationProvider.notifier)
            .calculateDistanceTo(restaurant.latitude, restaurant.longitude);
        return (restaurant, distance);
      })
      .toList();

  // Sort by distance
  restaurantsWithDistance.sort((a, b) => a.$2.compareTo(b.$2));

  // Return restaurants within 10km
  return restaurantsWithDistance
      .where((item) => item.$2 <= 10.0)
      .map((item) => item.$1)
      .toList();
});

// Top rated restaurants provider
final topRatedRestaurantsProvider = FutureProvider<List<RestaurantModel>>((
  ref,
) async {
  final restaurants = await ref.watch(restaurantsProvider.future);

  // Filter active restaurants with good ratings
  final topRated = restaurants
      .where((restaurant) => restaurant.isActive && restaurant.reviewCount >= 5)
      .toList();

  // Sort by rating descending, then by review count
  topRated.sort((a, b) {
    final ratingComparison = b.rating.compareTo(a.rating);
    if (ratingComparison != 0) return ratingComparison;
    return b.reviewCount.compareTo(a.reviewCount);
  });

  return topRated.take(20).toList();
});
