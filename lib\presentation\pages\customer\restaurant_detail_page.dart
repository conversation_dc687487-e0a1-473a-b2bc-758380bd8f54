import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../providers/customer_provider.dart';
import '../../providers/cart_provider.dart';
import '../../../data/models/shop_model.dart';
import '../../../data/models/product_model.dart';
import '../../../core/constants/screen_ids.dart';
import '../../../core/constants/app_colors.dart';

class RestaurantDetailPage extends ConsumerStatefulWidget {
  final String restaurantId;

  const RestaurantDetailPage({super.key, required this.restaurantId});

  @override
  ConsumerState<RestaurantDetailPage> createState() =>
      _RestaurantDetailPageState();
}

class _RestaurantDetailPageState extends ConsumerState<RestaurantDetailPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final restaurantAsync = ref.watch(shopDetailProvider(widget.restaurantId));
    final productsAsync = ref.watch(
      shopProductsProvider(ShopProductsParams(shopId: widget.restaurantId)),
    );
    final cartState = ref.watch(cartProvider);

    return Scaffold(
      body: restaurantAsync.when(
        data: (restaurant) {
          return CustomScrollView(
            slivers: [
              // App Bar with Restaurant Image
              SliverAppBar(
                expandedHeight: 200,
                pinned: true,
                flexibleSpace: FlexibleSpaceBar(
                  background: Container(
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      image: restaurant.image != null
                          ? DecorationImage(
                              image: NetworkImage(restaurant.image!),
                              fit: BoxFit.cover,
                            )
                          : null,
                    ),
                    child: restaurant.image == null
                        ? const Center(
                            child: Icon(
                              Icons.store,
                              size: 80,
                              color: Colors.grey,
                            ),
                          )
                        : null,
                  ),
                ),
                actions: [
                  IconButton(
                    icon: const Icon(Icons.favorite_border),
                    onPressed: () {
                      // Add to favorites
                    },
                  ),
                  IconButton(
                    icon: const Icon(Icons.share),
                    onPressed: () {
                      // Share restaurant
                    },
                  ),
                ],
              ),

              // Restaurant Info
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        restaurant.name,
                        style: Theme.of(context).textTheme.headlineSmall
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      if (restaurant.description != null)
                        Text(
                          restaurant.description!,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 16,
                          ),
                        ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          _buildInfoChip(
                            icon: Icons.star,
                            label: '${restaurant.rating}',
                            color: Colors.orange,
                          ),
                          const SizedBox(width: 12),
                          if (restaurant.businessHours != null)
                            _buildInfoChip(
                              icon: Icons.access_time,
                              label: restaurant.businessHours!,
                              color: Colors.blue,
                            ),
                          const SizedBox(width: 12),
                          if (restaurant.minimumOrder != null)
                            _buildInfoChip(
                              icon: Icons.payments,
                              label:
                                  '${restaurant.minimumOrder!.toStringAsFixed(0)}đ',
                              color: Colors.green,
                            ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      if (!restaurant.isActive)
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.red[50],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.red[200]!),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.schedule, color: Colors.red[700]),
                              const SizedBox(width: 8),
                              Text(
                                'Cửa hàng hiện đang tạm ngưng hoạt động',
                                style: TextStyle(
                                  color: Colors.red[700],
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ),

              // Tab Bar
              SliverPersistentHeader(
                pinned: true,
                delegate: _SliverTabBarDelegate(
                  TabBar(
                    controller: _tabController,
                    tabs: const [
                      Tab(text: 'Menu'),
                      Tab(text: 'Đánh giá'),
                      Tab(text: 'Thông tin'),
                    ],
                  ),
                ),
              ),

              // Tab Content
              SliverFillRemaining(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    // Menu Tab
                    productsAsync.when(
                      data: (products) =>
                          _buildMenuTab(context, products, restaurant),
                      loading: () =>
                          const Center(child: CircularProgressIndicator()),
                      error: (error, stack) =>
                          Center(child: Text('Lỗi: $error')),
                    ),
                    // Reviews Tab
                    _buildReviewsTab(context, restaurant),
                    // Info Tab
                    _buildInfoTab(context, restaurant),
                  ],
                ),
              ),
            ],
          );
        },
        loading: () =>
            const Scaffold(body: Center(child: CircularProgressIndicator())),
        error: (error, stack) =>
            Scaffold(body: Center(child: Text('Lỗi: $error'))),
      ),
      bottomNavigationBar: cartState.isNotEmpty
          ? Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: ElevatedButton(
                onPressed: () {
                  context.goNamed(ScreenIds.cart);
                },
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('${cartState.itemCount} món'),
                    Text(
                      'Xem giỏ hàng • ${cartState.total.toStringAsFixed(0)}đ',
                    ),
                  ],
                ),
              ),
            )
          : null,
    );
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuTab(
    BuildContext context,
    List<ProductModel> products,
    ShopModel restaurant,
  ) {
    if (products.isEmpty) {
      return const Center(child: Text('Cửa hàng chưa có món nào'));
    }

    // Group products by category (using categoryId since category object is not available)
    final Map<String, List<ProductModel>> productsByCategory = {};
    for (final product in products) {
      final categoryName = 'Danh mục ${product.categoryId ?? 'Khác'}';
      productsByCategory.putIfAbsent(categoryName, () => []).add(product);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: productsByCategory.keys.length,
      itemBuilder: (context, index) {
        final categoryId = productsByCategory.keys.elementAt(index);
        final categoryProducts = productsByCategory[categoryId]!;
        return _buildMenuSection(
          context,
          categoryId,
          categoryProducts,
          restaurant,
        );
      },
    );
  }

  Widget _buildMenuSection(
    BuildContext context,
    String sectionName,
    List<ProductModel> products,
    ShopModel restaurant,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Text(
            sectionName,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ),
        // Products in this section
        ...products.map(
          (product) => _buildProductCard(context, product, restaurant),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildProductCard(
    BuildContext context,
    ProductModel product,
    ShopModel restaurant,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    product.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    product.description ?? '',
                    style: TextStyle(color: Colors.grey[600], fontSize: 14),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    '${product.price.toStringAsFixed(0)}đ',
                    style: const TextStyle(
                      color: Color(0xFF000000), // Chữ đen trên nền card trắng
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Column(
              children: [
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.fastfood, size: 40),
                ),
                const SizedBox(height: 8),
                ElevatedButton(
                  onPressed: restaurant.isActive
                      ? () {
                          _showAddToCartDialog(context, product, restaurant);
                        }
                      : null,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                  ),
                  child: const Text('Thêm'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReviewsTab(BuildContext context, ShopModel restaurant) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Rating Summary
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Column(
                  children: [
                    Text(
                      '${restaurant.rating ?? 0}',
                      style: const TextStyle(
                        fontSize: 48,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                    Row(
                      children: List.generate(5, (index) {
                        return Icon(
                          index < (restaurant.rating ?? 0).floor()
                              ? Icons.star
                              : Icons.star_border,
                          color: Colors.orange,
                          size: 20,
                        );
                      }),
                    ),
                  ],
                ),
                const SizedBox(width: 24),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${restaurant.reviewCount ?? 0} đánh giá',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      _buildRatingBar(5, 0.6),
                      _buildRatingBar(4, 0.3),
                      _buildRatingBar(3, 0.1),
                      _buildRatingBar(2, 0.0),
                      _buildRatingBar(1, 0.0),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Write Review Button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () {
                _showWriteReviewDialog(context, restaurant);
              },
              icon: const Icon(Icons.edit),
              label: const Text('Viết đánh giá'),
            ),
          ),
          const SizedBox(height: 24),

          // Reviews List
          Text(
            'Đánh giá từ khách hàng',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          // Sample Reviews (in real app, this would come from API)
          ..._buildSampleReviews(),
        ],
      ),
    );
  }

  Widget _buildRatingBar(int stars, double percentage) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Text('$stars'),
          const SizedBox(width: 8),
          Expanded(
            child: LinearProgressIndicator(
              value: percentage,
              backgroundColor: Colors.grey[300],
              valueColor: const AlwaysStoppedAnimation<Color>(Colors.orange),
            ),
          ),
          const SizedBox(width: 8),
          Text('${(percentage * 100).toInt()}%'),
        ],
      ),
    );
  }

  List<Widget> _buildSampleReviews() {
    final sampleReviews = [
      {
        'name': 'Nguyễn Văn A',
        'rating': 5,
        'comment': 'Món ăn rất ngon, giao hàng nhanh. Sẽ đặt lại!',
        'date': '2 ngày trước',
      },
      {
        'name': 'Trần Thị B',
        'rating': 4,
        'comment': 'Chất lượng tốt, giá cả hợp lý. Nhà hàng phục vụ chu đáo.',
        'date': '1 tuần trước',
      },
      {
        'name': 'Lê Văn C',
        'rating': 5,
        'comment': 'Đồ ăn tươi ngon, đóng gói cẩn thận. Rất hài lòng!',
        'date': '2 tuần trước',
      },
    ];

    return sampleReviews.map((review) => _buildReviewCard(review)).toList();
  }

  Widget _buildReviewCard(Map<String, dynamic> review) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: AppColors.primary,
                  child: Text(
                    review['name'][0],
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        review['name'],
                        style: const TextStyle(fontWeight: FontWeight.w600),
                      ),
                      Row(
                        children: [
                          ...List.generate(5, (index) {
                            return Icon(
                              index < review['rating']
                                  ? Icons.star
                                  : Icons.star_border,
                              color: Colors.orange,
                              size: 16,
                            );
                          }),
                          const SizedBox(width: 8),
                          Text(
                            review['date'],
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(review['comment']),
          ],
        ),
      ),
    );
  }

  void _showWriteReviewDialog(BuildContext context, ShopModel restaurant) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Viết đánh giá'),
        content: const Text(
          'Chức năng viết đánh giá sẽ được cập nhật trong phiên bản tiếp theo.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Đóng'),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoTab(BuildContext context, ShopModel restaurant) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoRow(
            'Địa chỉ',
            restaurant.address ?? 'Chưa cập nhật địa chỉ',
          ),
          _buildInfoRow('Điện thoại', restaurant.phone ?? 'Chưa cập nhật'),
          _buildInfoRow(
            'Giờ mở cửa',
            restaurant.businessHours ?? 'Chưa cập nhật',
          ),
          _buildInfoRow(
            'Đơn tối thiểu',
            restaurant.minimumOrder != null
                ? '${restaurant.minimumOrder!.toStringAsFixed(0)}đ'
                : 'Chưa cập nhật',
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _showAddToCartDialog(
    BuildContext context,
    ProductModel product,
    ShopModel restaurant,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) =>
          AddToCartBottomSheet(product: product, restaurant: restaurant),
    );
  }
}

class _SliverTabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar tabBar;

  _SliverTabBarDelegate(this.tabBar);

  @override
  double get minExtent => tabBar.preferredSize.height;

  @override
  double get maxExtent => tabBar.preferredSize.height;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: tabBar,
    );
  }

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return false;
  }
}

class AddToCartBottomSheet extends ConsumerStatefulWidget {
  final dynamic product;
  final dynamic restaurant;

  const AddToCartBottomSheet({
    super.key,
    required this.product,
    required this.restaurant,
  });

  @override
  ConsumerState<AddToCartBottomSheet> createState() =>
      _AddToCartBottomSheetState();
}

class _AddToCartBottomSheetState extends ConsumerState<AddToCartBottomSheet> {
  int quantity = 1;
  dynamic selectedVariant;
  List<dynamic> selectedToppings = [];
  final TextEditingController noteController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final product = widget.product;
    double totalPrice = _calculateTotalPrice();

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product Info
          Row(
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.fastfood, size: 40),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.name,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    ),
                    Text(
                      '${product.price.toStringAsFixed(0)}đ',
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Variants
          if (product.variants.isNotEmpty) ...[
            const Text(
              'Chọn size',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            const SizedBox(height: 8),
            ...product.variants.map<Widget>((ProductVariantModel variant) {
              return RadioListTile(
                title: Text(variant.name),
                subtitle: variant.additionalPrice > 0
                    ? Text('+${variant.additionalPrice.toStringAsFixed(0)}đ')
                    : null,
                value: variant,
                groupValue: selectedVariant,
                onChanged: (value) {
                  setState(() {
                    selectedVariant = value;
                  });
                },
              );
            }).toList(),
            const SizedBox(height: 16),
          ],

          // Toppings
          if (product.toppings.isNotEmpty) ...[
            const Text(
              'Topping',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            const SizedBox(height: 8),
            ...product.toppings.map<Widget>((ProductToppingModel topping) {
              return CheckboxListTile(
                title: Text(topping.name),
                subtitle: Text('+${topping.price.toStringAsFixed(0)}đ'),
                value: selectedToppings.any((t) => t.id == topping.id),
                onChanged: (value) {
                  setState(() {
                    if (value == true) {
                      selectedToppings.add(topping);
                    } else {
                      selectedToppings.removeWhere((t) => t.id == topping.id);
                    }
                  });
                },
              );
            }).toList(),
            const SizedBox(height: 16),
          ],

          // Note
          TextField(
            controller: noteController,
            decoration: const InputDecoration(
              labelText: 'Ghi chú',
              hintText: 'Yêu cầu đặc biệt...',
              border: OutlineInputBorder(),
            ),
            maxLines: 2,
          ),
          const SizedBox(height: 24),

          // Quantity and Add to Cart
          Row(
            children: [
              // Quantity
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: quantity > 1
                          ? () {
                              setState(() {
                                quantity--;
                              });
                            }
                          : null,
                      icon: const Icon(Icons.remove),
                    ),
                    Text(
                      '$quantity',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        setState(() {
                          quantity++;
                        });
                      },
                      icon: const Icon(Icons.add),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),

              // Add to Cart Button
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    ref
                        .read(cartProvider.notifier)
                        .addItem(
                          product: product,
                          restaurantId: widget.restaurant.id.toString(),
                          restaurantName: widget.restaurant.name,
                          quantity: quantity,
                          selectedVariant: selectedVariant,
                          selectedToppings: selectedToppings.cast(),
                          note: noteController.text.trim().isEmpty
                              ? null
                              : noteController.text.trim(),
                        );
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Đã thêm vào giỏ hàng'),
                        duration: Duration(seconds: 2),
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: Text(
                    'Thêm vào giỏ • ${totalPrice.toStringAsFixed(0)}đ',
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  double _calculateTotalPrice() {
    double basePrice = widget.product.price;

    if (selectedVariant != null) {
      basePrice += selectedVariant!.additionalPrice;
    }

    for (final topping in selectedToppings) {
      basePrice += topping.price;
    }

    return basePrice * quantity;
  }
}
