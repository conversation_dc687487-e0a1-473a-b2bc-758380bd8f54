import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/user_model.dart';
import '../models/product_model.dart';
import '../models/order_model.dart';
import '../models/restaurant_model.dart';
import '../models/shop_model.dart';

abstract class LocalDataSource {
  Future<List<UserModel>> getUsers();
  Future<List<ProductModel>> getProducts();
  Future<List<OrderModel>> getOrders();
  Future<List<RestaurantModel>> getRestaurants();
  Future<List<CategoryModel>> getCategories();
  Future<List<VoucherModel>> getVouchers();
  Future<List<PostModel>> getPosts();
}

class LocalDataSourceImpl implements LocalDataSource {
  @override
  Future<List<UserModel>> getUsers() async {
    final String response = await rootBundle.loadString(
      'assets/data/users.json',
    );
    final List<dynamic> data = json.decode(response);
    return data.map((json) => UserModel.fromJson(json)).toList();
  }

  @override
  Future<List<ProductModel>> getProducts() async {
    final String response = await rootBundle.loadString(
      'assets/data/products.json',
    );
    final List<dynamic> data = json.decode(response);
    return data.map((json) => ProductModel.fromJson(json)).toList();
  }

  @override
  Future<List<OrderModel>> getOrders() async {
    final String response = await rootBundle.loadString(
      'assets/data/orders.json',
    );
    final List<dynamic> data = json.decode(response);
    return data.map((json) => OrderModel.fromJson(json)).toList();
  }

  @override
  Future<List<RestaurantModel>> getRestaurants() async {
    final String response = await rootBundle.loadString(
      'assets/data/restaurants.json',
    );
    final List<dynamic> data = json.decode(response);
    return data.map((json) => RestaurantModel.fromJson(json)).toList();
  }

  @override
  Future<List<CategoryModel>> getCategories() async {
    final String response = await rootBundle.loadString(
      'assets/data/categories.json',
    );
    final List<dynamic> data = json.decode(response);
    return data.map((json) => CategoryModel.fromJson(json)).toList();
  }

  @override
  Future<List<VoucherModel>> getVouchers() async {
    final String response = await rootBundle.loadString(
      'assets/data/vouchers.json',
    );
    final List<dynamic> data = json.decode(response);
    return data.map((json) => VoucherModel.fromJson(json)).toList();
  }

  @override
  Future<List<PostModel>> getPosts() async {
    final String response = await rootBundle.loadString(
      'assets/data/posts.json',
    );
    final List<dynamic> data = json.decode(response);
    return data.map((json) => PostModel.fromJson(json)).toList();
  }
}
