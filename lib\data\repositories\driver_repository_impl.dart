import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import '../../core/errors/failures.dart';
import '../../domain/repositories/driver_repository.dart';
import '../datasources/driver_api_service.dart';
import '../models/order_model.dart';
import '../models/driver_stats_model.dart';
import '../models/api_response_model.dart';

class DriverRepositoryImpl implements DriverRepository {
  final DriverApiService driverApiService;

  DriverRepositoryImpl({required this.driverApiService});

  @override
  Future<Either<Failure, List<OrderModel>>> getAvailableOrders() async {
    try {
      final response = await driverApiService.getAvailableOrders(
        1, // page
        20, // per_page
        null, // pickup_latitude
        null, // pickup_longitude
        null, // max_distance
      );

      if (response.success && response.data != null) {
        final List<dynamic> ordersData = response.data as List<dynamic>;
        final orders = ordersData
            .map((json) => OrderModel.fromJson(json))
            .toList();
        return Right(orders);
      } else {
        return Left(ServerFailure(response.message));
      }
    } on DioException catch (e) {
      return Left(
        ServerFailure('Failed to get available orders: ${e.message}'),
      );
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, OrderModel>> getOrderDetail(String orderId) async {
    try {
      final response = await driverApiService.getOrderDetail(orderId);

      if (response.success && response.data != null) {
        final order = OrderModel.fromJson(response.data);
        return Right(order);
      } else {
        return Left(ServerFailure(response.message));
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        return Left(NotFoundFailure('Order not found'));
      }
      return Left(ServerFailure('Failed to get order detail: ${e.message}'));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, void>> acceptOrder(String orderId) async {
    try {
      await driverApiService.acceptOrder(orderId);
      return const Right(null);
    } on DioException catch (e) {
      if (e.response?.statusCode == 403) {
        return Left(ValidationFailure('Cannot accept more orders'));
      }
      if (e.response?.statusCode == 404) {
        return Left(NotFoundFailure('Order not available'));
      }
      return Left(ServerFailure('Failed to accept order: ${e.message}'));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, void>> updateOrderStatus(
    String orderId,
    String status,
  ) async {
    try {
      final statusData = {'status': status};
      await driverApiService.updateOrderStatus(orderId, statusData);
      return const Right(null);
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        return Left(NotFoundFailure('Order not found'));
      }
      return Left(ServerFailure('Failed to update order status: ${e.message}'));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, DriverStatsModel>> getDriverStats(String? date) async {
    try {
      final response = await driverApiService.getDriverStats(date, null);

      if (response.success && response.data != null) {
        final stats = DriverStatsModel.fromJson(response.data);
        return Right(stats);
      } else {
        return Left(ServerFailure(response.message));
      }
    } on DioException catch (e) {
      return Left(ServerFailure('Failed to get driver stats: ${e.message}'));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred'));
    }
  }
}
