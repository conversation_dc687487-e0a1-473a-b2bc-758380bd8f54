import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../core/theme/app_theme.dart';

/// =============================================================================
/// THEME PROVIDER - Quản lý theme switching
/// =============================================================================

/// Enum cho các theme có sẵn
enum AppThemeType {
  yellowBlack('YellowBlack', 'Vàng sáng', 'Theme vàng sáng với chữ đen'),
  green('Green', 'Xanh lá', 'Theme xanh lá tươi mát'),
  blue('Blue', 'Xanh dương', 'Theme xanh dương chuyên nghiệp'),
  orange('Orange', 'Cam', 'Theme cam năng động'),
  purple('Purple', 'Tím', 'Theme tím sang trọng'),
  red('Red', 'Đỏ', 'Theme đỏ nổi bật');

  const AppThemeType(this.key, this.displayName, this.description);
  
  final String key;
  final String displayName;
  final String description;

  /// Lấy ThemeData tương ứng
  ThemeData get themeData {
    switch (this) {
      case AppThemeType.yellowBlack:
        return AppTheme.lightTheme;
      case AppThemeType.green:
        return AppTheme.greenTheme;
      case AppThemeType.blue:
        return AppTheme.blueTheme;
      case AppThemeType.orange:
        return AppTheme.orangeTheme;
      case AppThemeType.purple:
        return AppTheme.purpleTheme;
      case AppThemeType.red:
        return AppTheme.redTheme;
    }
  }

  /// Lấy màu chính của theme
  Color get primaryColor {
    switch (this) {
      case AppThemeType.yellowBlack:
        return const Color(0xFFFFEB3B); // Vàng sáng
      case AppThemeType.green:
        return const Color(0xFF4CAF50); // Xanh lá
      case AppThemeType.blue:
        return const Color(0xFF2196F3); // Xanh dương
      case AppThemeType.orange:
        return const Color(0xFFFF9800); // Cam
      case AppThemeType.purple:
        return const Color(0xFF9C27B0); // Tím
      case AppThemeType.red:
        return const Color(0xFFF44336); // Đỏ
    }
  }

  /// Lấy màu phụ của theme
  Color get secondaryColor {
    switch (this) {
      case AppThemeType.yellowBlack:
        return const Color(0xFF070000); // Đen
      case AppThemeType.green:
        return const Color(0xFF2196F3); // Xanh dương
      case AppThemeType.blue:
        return const Color(0xFF4CAF50); // Xanh lá
      case AppThemeType.orange:
        return const Color(0xFF3F51B5); // Tím
      case AppThemeType.purple:
        return const Color(0xFF00BCD4); // Cyan
      case AppThemeType.red:
        return const Color(0xFF607D8B); // Xanh xám
    }
  }
}

/// =============================================================================
/// THEME NOTIFIER - Quản lý theme state
/// =============================================================================

class ThemeNotifier extends StateNotifier<AppThemeType> {
  static const String _themeKey = 'selected_theme';

  ThemeNotifier() : super(AppThemeType.yellowBlack) {
    _loadSavedTheme();
  }

  /// Load theme đã lưu từ SharedPreferences
  Future<void> _loadSavedTheme() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedThemeKey = prefs.getString(_themeKey) ?? 'YellowBlack';
      
      // Tìm theme từ key
      final theme = AppThemeType.values.firstWhere(
        (t) => t.key == savedThemeKey,
        orElse: () => AppThemeType.yellowBlack,
      );
      
      if (theme != state) {
        state = theme;
      }
    } catch (e) {
      print('Error loading theme: $e');
    }
  }

  /// Thay đổi theme và lưu vào SharedPreferences
  Future<void> changeTheme(AppThemeType newTheme) async {
    try {
      state = newTheme;
      
      // Lưu vào SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_themeKey, newTheme.key);
      
    } catch (e) {
      print('Error changing theme: $e');
    }
  }
}

/// =============================================================================
/// PROVIDERS - Các provider chính
/// =============================================================================

/// Provider chính cho theme
final themeProvider = StateNotifierProvider<ThemeNotifier, AppThemeType>((ref) {
  return ThemeNotifier();
});

/// Provider để lấy ThemeData hiện tại
final currentThemeDataProvider = Provider<ThemeData>((ref) {
  return ref.watch(themeProvider).themeData;
});

/// Provider để lấy tên theme hiện tại
final currentThemeNameProvider = Provider<String>((ref) {
  return ref.watch(themeProvider).displayName;
});

/// Provider để lấy màu chính hiện tại
final currentPrimaryColorProvider = Provider<Color>((ref) {
  return ref.watch(themeProvider).primaryColor;
});

/// Provider để lấy màu phụ hiện tại
final currentSecondaryColorProvider = Provider<Color>((ref) {
  return ref.watch(themeProvider).secondaryColor;
});
