import 'package:flutter/material.dart';

/// =============================================================================
/// APP THEME COLORS - CENTRALIZED COLOR MANAGEMENT
/// =============================================================================
///
/// Hệ thống màu sắc tập trung, dễ chỉnh sửa và bảo trì
/// Chỉ cần thay đổi các giá trị ở đây để cập nhật toàn bộ ứng dụng
///
/// HƯỚNG DẪN SỬ DỤNG:
/// 1. Thay đổi BASE_COLORS để đổi toàn bộ theme
/// 2. Thay đổi SEMANTIC_COLORS để điều chỉnh ý nghĩa màu sắc
/// 3. Thay đổi COMPONENT_COLORS để tùy chỉnh từng component
/// =============================================================================

class AppColors {
  // =============================================================================
  // BASE COLORS - Màu cơ bản (Theme Vàng-Đen mới)
  // =============================================================================

  /// Màu chính của ứng dụng - Vàng
  static const Color _primaryBase = Color(0xFFFFEB3B); // Vàng sáng
  static const Color _primaryDark = Color(0xFFFBC02D); // Vàng đậm
  static const Color _primaryLight = Color(0xFFFFE082); // Vàng nhạt
  static const Color _primaryVeryLight = Color(0xFFFFF9C4); // Vàng rất nhạt

  /// Màu phụ (Button, accent) - Đen/Xanh lá đậm
  static const Color _secondaryBase = Color(0xFF070000); // Đen button
  static const Color _secondaryDark = Color(
    0xFF225A33,
  ); // Xanh lá đậm (tab active)
  static const Color _secondaryLight = Color(0xFF424242); // Xám đậm

  /// Màu nền và bề mặt
  static const Color _surfaceWhite = Color(0xFFFFFFFF); // Trắng (input)
  static const Color _surfaceLight = Color(0xFFFFF8E1); // Vàng rất nhạt

  /// Màu chữ cơ bản
  static const Color _textOnDark = Color(0xFFFFFFFF); // Trắng (trên nền tối)
  static const Color _textOnLight = Color(
    0xFF0D2949,
  ); // Đen xanh (trên nền sáng)
  static const Color _textPrimary = Color(0xFF0D2949); // Đen xanh chính
  static const Color _textButton = Color(0xFFFFEB3B); // Vàng (chữ button)
  static const Color _textSecondary = Color(0xFF37474F); // Xám đậm
  static const Color _textHint = Color(0xFF757575); // Xám hint

  // =============================================================================
  // SEMANTIC COLORS - Màu theo ý nghĩa (Dễ hiểu và sử dụng)
  // =============================================================================

  /// Màu chính của ứng dụng
  static const Color primary = _primaryBase;
  static const Color primaryDark = _primaryDark;
  static const Color primaryLight = _primaryLight;

  /// Màu nền chính
  static const Color background = _primaryBase; // Nền vàng sáng
  static const Color surface = _surfaceWhite; // Bề mặt trắng (Card, Dialog)
  static const Color surfaceVariant = _surfaceLight; // Bề mặt nhạt

  /// Màu chữ theo ngữ cảnh
  static const Color textOnBackground =
      _textPrimary; // Chữ đen xanh trên nền vàng
  static const Color textOnSurface =
      _textPrimary; // Chữ đen xanh trên bề mặt trắng
  static const Color textOnDark = _textOnDark; // Chữ trắng trên nền tối
  static const Color textOnLight = _textPrimary; // Chữ đen xanh trên nền sáng
  static const Color textSecondary = _textSecondary; // Chữ phụ
  static const Color textHint = _textHint; // Chữ gợi ý
  static const Color textDisabled = Color(0xFF9E9E9E); // Chữ bị vô hiệu hóa

  // =============================================================================
  // COMPONENT COLORS - Màu theo component (Dễ tùy chỉnh từng phần)
  // =============================================================================

  /// Input Field Colors
  static const Color inputBackground = _surfaceWhite; // Nền input trắng
  static const Color inputBorder = Color(0xFFE0E0E0); // Viền input xám nhạt
  static const Color inputFocusedBorder = _primaryBase; // Viền khi focus vàng
  static const Color inputText = Color(0xFF000000); // Chữ đen trong input
  static const Color inputHint = _textHint; // Chữ gợi ý

  /// Button Colors
  static const Color buttonPrimary = _secondaryBase; // Nền button đen
  static const Color buttonPrimaryText = _textButton; // Chữ button vàng
  static const Color buttonSecondary =
      _primaryLight; // Nền button phụ vàng nhạt
  static const Color buttonSecondaryText = _textPrimary; // Chữ button phụ đen
  static const Color buttonDisabled = Color(
    0xFFBDBDBD,
  ); // Button bị vô hiệu hóa
  static const Color buttonTextDisabled = Color(
    0xFF757575,
  ); // Chữ button vô hiệu hóa

  /// Card Colors
  static const Color cardBackground = _surfaceWhite; // Nền card trắng
  static const Color cardText = _textOnLight; // Chữ đen trên card
  static const Color cardShadow = Color(0x1F000000); // Bóng card

  /// Tab Colors
  static const Color tabBackground = _primaryLight; // Nền tab vàng nhạt
  static const Color tabActiveBackground =
      _secondaryDark; // Nền tab active xanh lá đậm
  static const Color tabText = _textPrimary; // Chữ tab đen xanh
  static const Color tabActiveText =
      _secondaryDark; // Chữ tab active xanh lá đậm
  static const Color tabIndicator = _secondaryDark; // Indicator tab xanh lá đậm

  /// Navigation Colors
  static const Color navBackground = _primaryLight; // Nền navigation vàng nhạt
  static const Color navSelected = _secondaryDark; // Item được chọn xanh lá đậm
  static const Color navUnselected = _textPrimary; // Item không được chọn đen

  /// App Bar Colors
  static const Color appBarBackground = _primaryBase; // Nền app bar vàng
  static const Color appBarText = _textPrimary; // Chữ app bar đen xanh
  static const Color appBarIcon = _textPrimary; // Icon app bar đen xanh

  /// Logo Colors (không có nền)
  static const Color logoBackground = Colors.transparent; // Nền logo trong suốt
  static const Color logoIcon = _primaryBase; // Icon logo vàng (fallback)

  /// Status Colors
  static const Color success = Color(0xFF4CAF50); // Thành công
  static const Color warning = Color(0xFFFF9800); // Cảnh báo
  static const Color error = Color(0xFFF44336); // Lỗi
  static const Color info = _secondaryBase; // Thông tin

  /// Icon Colors
  static const Color iconOnBackground =
      _textPrimary; // Icon trên nền vàng (đen)
  static const Color iconOnSurface =
      _textPrimary; // Icon trên bề mặt trắng (đen)
  static const Color iconPrimary = _textPrimary; // Icon chính (đen)
  static const Color iconSecondary = _textSecondary; // Icon phụ
  static const Color iconDisabled = Color(0xFFBDBDBD); // Icon vô hiệu hóa

  /// Border & Divider Colors
  static const Color divider = Color(0xFFE0E0E0); // Đường phân cách nhạt
  static const Color borderLight = Color(0xFFE0E0E0); // Viền nhạt
  static const Color borderMedium = Color(0xFFBDBDBD); // Viền trung bình
  static const Color borderDark = Color(0xFF757575); // Viền đậm

  // =============================================================================
  // LEGACY SUPPORT - Tương thích ngược (Deprecated - sẽ xóa trong tương lai)
  // =============================================================================

  /// @deprecated Sử dụng textOnBackground thay thế
  static const Color textPrimary = textOnBackground;

  /// @deprecated Sử dụng buttonPrimary thay thế
  static const Color buttonBackground = buttonPrimary;

  /// @deprecated Sử dụng buttonPrimaryText thay thế
  static const Color buttonText = buttonPrimaryText;

  /// @deprecated Sử dụng navBackground thay thế
  static const Color bottomNavBackground = navBackground;

  /// @deprecated Sử dụng navSelected thay thế
  static const Color bottomNavSelected = navSelected;

  /// @deprecated Sử dụng navUnselected thay thế
  static const Color bottomNavUnselected = navUnselected;

  /// @deprecated Sử dụng tabText thay thế
  static const Color tabSelected = tabActiveText;

  /// @deprecated Sử dụng tabText thay thế
  static const Color tabUnselected = tabText;

  /// @deprecated Sử dụng appBarBackground thay thế
  static const Color scaffoldBackground = appBarBackground;
}

/// App Color Scheme for Material 3
class AppColorScheme {
  static ColorScheme get lightColorScheme => const ColorScheme(
    brightness: Brightness.light,
    primary: AppColors.primary, // Xanh lá
    onPrimary: AppColors.textPrimary, // Chữ trắng trên nền xanh lá
    primaryContainer: AppColors.primaryLight, // Xanh lá nhạt
    onPrimaryContainer: Color(0xFF000000), // Chữ đen trên container xanh nhạt
    secondary: AppColors.buttonBackground, // Xanh dương
    onSecondary: AppColors.buttonText, // Chữ trắng trên xanh dương
    secondaryContainer: AppColors.surfaceVariant, // Xanh lá rất nhạt
    onSecondaryContainer: Color(0xFF000000), // Chữ đen trên container nhạt
    tertiary: AppColors.info, // Xanh dương thông tin
    onTertiary: AppColors.buttonText, // Chữ trắng
    tertiaryContainer: AppColors.surfaceVariant,
    onTertiaryContainer: Color(0xFF000000),
    error: AppColors.error,
    onError: AppColors.buttonText,
    errorContainer: Color(0xFFFFEBEE),
    onErrorContainer: AppColors.error,
    background: AppColors.background, // Nền xanh lá
    onBackground: AppColors.textPrimary, // Chữ trắng trên nền xanh lá
    surface: AppColors.surface, // Bề mặt trắng
    onSurface: Color(0xFF000000), // Chữ đen trên bề mặt trắng
    surfaceVariant: AppColors.surfaceVariant,
    onSurfaceVariant: Color(0xFF000000), // Chữ đen trên variant nhạt
    outline: AppColors.borderMedium,
    outlineVariant: AppColors.borderLight,
    shadow: AppColors.cardShadow,
    scrim: Color(0x80000000),
    inverseSurface: AppColors.buttonBackground,
    onInverseSurface: AppColors.buttonText,
    inversePrimary: AppColors.primaryDark,
    surfaceTint: AppColors.primary,
  );
}
