import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../presentation/providers/auth_provider.dart';
import '../../presentation/pages/auth/splash_page.dart';
import '../../presentation/pages/auth/login_page.dart';
import '../../presentation/pages/auth/phone_login_page.dart';
import '../../presentation/pages/auth/otp_verification_page.dart';
import '../../presentation/pages/auth/location_permission_page.dart';
import '../../presentation/pages/auth/register_page.dart';
import '../../presentation/pages/auth/forgot_password_page.dart';
import '../../presentation/pages/customer/customer_home_page.dart';
import '../../presentation/pages/customer/search_page.dart';
import '../../presentation/pages/customer/restaurant_detail_page.dart';
import '../../presentation/pages/customer/shops_list_page.dart';
import '../../presentation/pages/customer/products_list_page.dart';
import '../../presentation/pages/customer/category_products_page.dart';
import '../../presentation/pages/customer/cart_page.dart';
import '../../presentation/pages/customer/checkout_page.dart';
import '../../presentation/pages/customer/order_success_page.dart';
import '../../presentation/pages/customer/order_tracking_page.dart';
import '../../presentation/pages/customer/order_history_page.dart';
import '../../presentation/pages/customer/address_management_page.dart';
import '../../presentation/pages/customer/favorites_page.dart';
import '../../presentation/pages/customer/profile_page.dart';
import '../../presentation/pages/shop/shop_home_page.dart';
import '../../presentation/pages/shop/create_order_page.dart';
import '../../presentation/pages/shop/shop_orders_page.dart';
import '../../presentation/pages/shop/shop_products_page.dart';
import '../../presentation/pages/shop/shop_stats_page.dart';
import '../../presentation/pages/shipper/shipper_home_page.dart';
import '../../presentation/pages/shipper/available_orders_page.dart';
import '../../presentation/pages/shipper/order_detail_page.dart';
import '../../presentation/pages/shipper/stats_page.dart';
import '../../presentation/pages/settings/theme_settings_page.dart';
import '../../core/constants/app_constants.dart';
import '../../core/constants/screen_ids.dart';

class AppRouter {
  static final _rootNavigatorKey = GlobalKey<NavigatorState>();

  static GoRouter router(WidgetRef ref) {
    return GoRouter(
      navigatorKey: _rootNavigatorKey,
      initialLocation: '/',
      redirect: (context, state) {
        final authState = ref.read(authProvider);
        final isAuthenticated = authState.isAuthenticated;
        final user = authState.user;

        // If not authenticated and not on auth pages, redirect to login
        if (!isAuthenticated &&
            !state.fullPath!.startsWith('/login') &&
            !state.fullPath!.startsWith('/phone-login') &&
            !state.fullPath!.startsWith('/otp-verification') &&
            !state.fullPath!.startsWith('/register') &&
            !state.fullPath!.startsWith('/forgot-password') &&
            state.fullPath != '/') {
          return '/login';
        }

        // If authenticated and on auth pages, redirect to appropriate home
        if (isAuthenticated &&
            (state.fullPath == '/' ||
                state.fullPath!.startsWith('/login') ||
                state.fullPath!.startsWith('/phone-login') ||
                state.fullPath!.startsWith('/otp-verification') ||
                state.fullPath!.startsWith('/register') ||
                state.fullPath!.startsWith('/forgot-password'))) {
          return _getHomeRouteForRole(user?.role ?? AppConstants.roleCustomer);
        }

        return null;
      },
      routes: [
        // Splash Route
        GoRoute(
          path: '/',
          name: ScreenIds.splash,
          builder: (context, state) => const SplashPage(),
        ),

        // Auth Routes
        GoRoute(
          path: '/login',
          name: ScreenIds.authLoginEmail,
          builder: (context, state) => const LoginPage(),
        ),
        GoRoute(
          path: '/phone-login',
          name: ScreenIds.authLoginOtp,
          builder: (context, state) => const PhoneLoginPage(),
        ),
        GoRoute(
          path: '/otp-verification',
          name: 'otp-verification',
          builder: (context, state) {
            final extra = state.extra as Map<String, dynamic>?;
            final identifier = extra?['identifier'] ?? '';
            final type = extra?['type'] ?? 'phone';
            return OtpVerificationPage(identifier: identifier, type: type);
          },
        ),
        GoRoute(
          path: '/register',
          name: ScreenIds.register,
          builder: (context, state) => const RegisterPage(),
        ),
        GoRoute(
          path: '/forgot-password',
          name: ScreenIds.forgotPassword,
          builder: (context, state) => const ForgotPasswordPage(),
        ),
        GoRoute(
          path: '/location-permission',
          name: ScreenIds.permissionLocation,
          builder: (context, state) => const LocationPermissionPage(),
        ),

        // Customer Routes
        GoRoute(
          path: '/customer',
          name: ScreenIds.home,
          builder: (context, state) => const CustomerHomePage(),
          routes: [
            GoRoute(
              path: '/search',
              name: ScreenIds.search,
              builder: (context, state) => const SearchPage(),
            ),
            GoRoute(
              path: '/shops',
              name: 'customer_shops',
              builder: (context, state) => const ShopsListPage(),
            ),
            GoRoute(
              path: '/products',
              name: 'customer_products',
              builder: (context, state) => const ProductsListPage(),
            ),
            GoRoute(
              path: '/categories/:id/products',
              name: 'customer_category_products',
              builder: (context, state) {
                final id = int.parse(state.pathParameters['id']!);
                final name =
                    (state.extra as Map?)?['name'] as String? ?? 'Sản phẩm';
                return CategoryProductsPage(categoryId: id, categoryName: name);
              },
            ),
            GoRoute(
              path: '/restaurant/:id',
              name: ScreenIds.restaurantDetail,
              builder: (context, state) {
                final restaurantId = state.pathParameters['id']!;
                return RestaurantDetailPage(restaurantId: restaurantId);
              },
            ),
            GoRoute(
              path: '/cart',
              name: ScreenIds.cart,
              builder: (context, state) => const CartPage(),
            ),
            GoRoute(
              path: '/checkout',
              name: ScreenIds.checkoutReview,
              builder: (context, state) => const CheckoutPage(),
            ),
            GoRoute(
              path: '/order-success',
              name: 'order-success',
              builder: (context, state) => const OrderSuccessPage(),
            ),
            GoRoute(
              path: '/order-tracking/:orderId',
              name: 'order-tracking',
              builder: (context, state) {
                final orderId = state.pathParameters['orderId']!;
                return OrderTrackingPage(orderId: orderId);
              },
            ),
            GoRoute(
              path: '/orders',
              name: ScreenIds.accountOrderHistory,
              builder: (context, state) => const OrderHistoryPage(),
            ),
            GoRoute(
              path: '/favorites',
              name: 'favorites',
              builder: (context, state) => const FavoritesPage(),
            ),
            GoRoute(
              path: '/addresses',
              name: ScreenIds.accountAddresses,
              builder: (context, state) => const AddressManagementPage(),
            ),
            GoRoute(
              path: '/profile',
              name: ScreenIds.accountProfile,
              builder: (context, state) => const ProfilePage(),
            ),
            GoRoute(
              path: '/theme-settings',
              name: 'theme_settings',
              builder: (context, state) => const ThemeSettingsPage(),
            ),
          ],
        ),

        // Shop Routes
        GoRoute(
          path: '/shop',
          name: ScreenIds.shopHome,
          builder: (context, state) => const ShopHomePage(),
          routes: [
            GoRoute(
              path: '/create-order',
              name: ScreenIds.shopCreateOrder,
              builder: (context, state) => const CreateOrderPage(),
            ),
            GoRoute(
              path: '/orders',
              name: ScreenIds.shopOrderList,
              builder: (context, state) => const ShopOrdersPage(),
            ),
            GoRoute(
              path: '/products',
              name: ScreenIds.shopProductList,
              builder: (context, state) => const ShopProductsPage(),
            ),
            GoRoute(
              path: '/stats',
              name: ScreenIds.shopStats,
              builder: (context, state) => const ShopStatsPage(),
            ),
          ],
        ),

        // Shipper Routes
        GoRoute(
          path: '/shipper',
          name: ScreenIds.shipperHome,
          builder: (context, state) => const ShipperHomePage(),
          routes: [
            GoRoute(
              path: '/orders',
              name: ScreenIds.shipperOrderList,
              builder: (context, state) => const AvailableOrdersPage(),
            ),
            GoRoute(
              path: '/orders/:id',
              name: 'shipperOrderDetail',
              builder: (context, state) {
                final orderId = state.pathParameters['id']!;
                return OrderDetailPage(orderId: orderId);
              },
            ),
            GoRoute(
              path: '/stats',
              name: ScreenIds.shipperStats,
              builder: (context, state) => const StatsPage(),
            ),
          ],
        ),
      ],
    );
  }

  static String _getHomeRouteForRole(String role) {
    switch (role) {
      case AppConstants.roleShop:
        return '/shop';
      case AppConstants.roleShipper:
        return '/shipper';
      case AppConstants.roleCustomer:
      default:
        return '/customer';
    }
  }
}
