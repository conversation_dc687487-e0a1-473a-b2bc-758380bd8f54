import 'package:flutter_riverpod/flutter_riverpod.dart';

// Loading state for different operations
class LoadingState {
  final Map<String, bool> _loadingStates = {};

  bool isLoading(String key) => _loadingStates[key] ?? false;
  
  void setLoading(String key, bool loading) {
    _loadingStates[key] = loading;
  }

  bool get hasAnyLoading => _loadingStates.values.any((loading) => loading);
  
  Map<String, bool> get allStates => Map.unmodifiable(_loadingStates);
}

class LoadingNotifier extends StateNotifier<LoadingState> {
  LoadingNotifier() : super(LoadingState());

  void setLoading(String key, bool loading) {
    state.setLoading(key, loading);
    // Trigger rebuild
    state = LoadingState()
      .._loadingStates.addAll(state._loadingStates);
  }

  bool isLoading(String key) => state.isLoading(key);

  Future<T> withLoading<T>(String key, Future<T> Function() operation) async {
    setLoading(key, true);
    try {
      final result = await operation();
      return result;
    } finally {
      setLoading(key, false);
    }
  }
}

// Global loading provider
final loadingProvider = StateNotifierProvider<LoadingNotifier, LoadingState>(
  (ref) => LoadingNotifier(),
);

// Specific loading keys
class LoadingKeys {
  static const String login = 'login';
  static const String register = 'register';
  static const String logout = 'logout';
  static const String fetchShops = 'fetch_shops';
  static const String fetchProducts = 'fetch_products';
  static const String fetchOrders = 'fetch_orders';
  static const String createOrder = 'create_order';
  static const String updateOrder = 'update_order';
  static const String cancelOrder = 'cancel_order';
  static const String acceptOrder = 'accept_order';
  static const String updateOrderStatus = 'update_order_status';
  static const String fetchProfile = 'fetch_profile';
  static const String updateProfile = 'update_profile';
  static const String fetchStats = 'fetch_stats';
  static const String uploadImage = 'upload_image';
  static const String sendOtp = 'send_otp';
  static const String verifyOtp = 'verify_otp';
}

// Extension for easier usage
extension LoadingProviderExtension on WidgetRef {
  LoadingNotifier get loading => read(loadingProvider.notifier);
  
  bool isLoading(String key) => watch(loadingProvider).isLoading(key);
  
  Future<T> withLoading<T>(String key, Future<T> Function() operation) {
    return read(loadingProvider.notifier).withLoading(key, operation);
  }
}
