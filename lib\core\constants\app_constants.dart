class AppConstants {
  // App Info
  static const String appName = 'Shipper19';
  static const String appVersion = '1.0.0';

  // API Base URLs - Laravel Backend
  // static const String baseUrl = 'https://shipper19.webest.asia'; // Change this to your Laravel backend URL
  static const String baseUrl = 'http://127.0.0.1:8000';
  static const String apiVersion = '/api/v1';

  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userRoleKey = 'user_role';
  static const String userIdKey = 'user_id';
  static const String isFirstTimeKey = 'is_first_time';

  // User Roles
  static const String roleShop = 'SHOP';
  static const String roleShipper = 'SHIPPER';
  static const String roleCustomer = 'CUSTOMER';
  static const String roleAdmin = 'ADMIN';

  // Order Status
  static const String orderStatusNew = 'NEW';
  static const String orderStatusPendingDriver = 'PENDING_DRIVER';
  static const String orderStatusPicking = 'PICKING';
  static const String orderStatusDelivering = 'DELIVERING';
  static const String orderStatusCompleted = 'COMPLETED';
  static const String orderStatusCancelled = 'CANCELLED';
  static const String orderStatusCancelledByShop = 'CANCELLED_BY_SHOP';
  static const String orderStatusCancelledByCustomer = 'CANCELLED_BY_CUSTOMER';
  static const String orderStatusCancelledNoDriver = 'CANCELLED_NO_DRIVER';
  static const String orderStatusCancelledByAdmin = 'CANCELLED_BY_ADMIN';

  // Payment Methods
  static const String paymentCash = 'CASH';
  static const String paymentBankTransfer = 'BANK_TRANSFER';
  static const String paymentEWallet = 'E_WALLET';

  // Limits
  static const int maxShipperOrders = 3;
  static const int orderTimeoutMinutes = 2;
  static const int searchResultsPerPage = 20;

  // Default Values
  static const double defaultLatitude = 10.8231;
  static const double defaultLongitude = 106.6297; // Ho Chi Minh City
  static const double defaultZoom = 15.0;
}
