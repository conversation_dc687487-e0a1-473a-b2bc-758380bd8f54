import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../providers/auth_provider.dart';
import '../../providers/theme_provider.dart';
import '../../../core/constants/screen_ids.dart';

class ProfilePage extends ConsumerWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authProvider);
    final user = authState.user;

    return Scaffold(
      appBar: AppBar(
        title: const Text('<PERSON><PERSON><PERSON> kho<PERSON>n'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              // Navigate to settings
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // User Info Section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).primaryColor,
                    Theme.of(context).primaryColor.withOpacity(0.8),
                  ],
                ),
              ),
              child: Column(
                children: [
                  CircleAvatar(
                    radius: 50,
                    backgroundColor: Colors.white,
                    child: user?.avatar != null
                        ? ClipOval(
                            child: Image.network(
                              user!.avatar!,
                              width: 100,
                              height: 100,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  const Icon(Icons.person, size: 50),
                            ),
                          )
                        : const Icon(
                            Icons.person,
                            size: 50,
                            color: Colors.grey,
                          ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    user?.name ?? 'Khách hàng',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    user?.email ?? '',
                    style: const TextStyle(color: Colors.white70, fontSize: 16),
                  ),
                  if (user?.phone != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      user!.phone!,
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ],
              ),
            ),

            // Menu Items
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Order Section
                  _buildSectionTitle('Đơn hàng'),
                  _buildMenuItem(
                    icon: Icons.receipt_long,
                    title: 'Lịch sử đơn hàng',
                    subtitle: 'Xem tất cả đơn hàng đã đặt',
                    onTap: () => context.goNamed(ScreenIds.accountOrderHistory),
                  ),
                  _buildMenuItem(
                    icon: Icons.favorite,
                    title: 'Món ăn yêu thích',
                    subtitle: 'Danh sách món ăn đã lưu',
                    onTap: () {
                      context.push('/customer/favorites');
                    },
                  ),
                  _buildMenuItem(
                    icon: Icons.location_on,
                    title: 'Địa chỉ giao hàng',
                    subtitle: 'Quản lý địa chỉ giao hàng',
                    onTap: () {
                      context.push('/customer/addresses');
                    },
                  ),

                  const SizedBox(height: 24),

                  // Payment Section
                  _buildSectionTitle('Thanh toán'),
                  _buildMenuItem(
                    icon: Icons.account_balance_wallet,
                    title: 'Ví của tôi',
                    subtitle: 'Số dư: 0đ',
                    onTap: () {
                      _showComingSoon(context);
                    },
                  ),
                  _buildMenuItem(
                    icon: Icons.credit_card,
                    title: 'Phương thức thanh toán',
                    subtitle: 'Thẻ tín dụng, ví điện tử',
                    onTap: () {
                      _showComingSoon(context);
                    },
                  ),
                  _buildMenuItem(
                    icon: Icons.local_offer,
                    title: 'Voucher của tôi',
                    subtitle: 'Mã giảm giá có sẵn',
                    onTap: () {
                      _showComingSoon(context);
                    },
                  ),

                  const SizedBox(height: 24),

                  // Support Section
                  _buildSectionTitle('Hỗ trợ'),
                  _buildMenuItem(
                    icon: Icons.help_outline,
                    title: 'Câu hỏi thường gặp',
                    subtitle: 'Tìm câu trả lời nhanh chóng',
                    onTap: () {
                      _showComingSoon(context);
                    },
                  ),
                  _buildMenuItem(
                    icon: Icons.support_agent,
                    title: 'Liên hệ hỗ trợ',
                    subtitle: 'Chat với đội ngũ hỗ trợ',
                    onTap: () {
                      _showComingSoon(context);
                    },
                  ),
                  _buildMenuItem(
                    icon: Icons.star_rate,
                    title: 'Đánh giá ứng dụng',
                    subtitle: 'Chia sẻ trải nghiệm của bạn',
                    onTap: () {
                      _showComingSoon(context);
                    },
                  ),

                  const SizedBox(height: 24),

                  // Settings Section
                  _buildSectionTitle('Cài đặt'),
                  _buildMenuItem(
                    icon: Icons.person_outline,
                    title: 'Thông tin cá nhân',
                    subtitle: 'Chỉnh sửa thông tin tài khoản',
                    onTap: () {
                      _showEditProfileDialog(context, user);
                    },
                  ),
                  _buildMenuItem(
                    icon: Icons.notifications_outlined,
                    title: 'Thông báo',
                    subtitle: 'Cài đặt thông báo',
                    onTap: () {
                      _showComingSoon(context);
                    },
                  ),
                  _buildThemeMenuItem(context, ref),
                  _buildMenuItem(
                    icon: Icons.language,
                    title: 'Ngôn ngữ',
                    subtitle: 'Tiếng Việt',
                    onTap: () {
                      _showComingSoon(context);
                    },
                  ),
                  _buildMenuItem(
                    icon: Icons.security,
                    title: 'Bảo mật',
                    subtitle: 'Đổi mật khẩu, xác thực 2 lớp',
                    onTap: () {
                      _showComingSoon(context);
                    },
                  ),

                  const SizedBox(height: 32),

                  // Logout Button
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton.icon(
                      onPressed: () => _showLogoutDialog(context, ref),
                      icon: const Icon(Icons.logout, color: Colors.red),
                      label: const Text(
                        'Đăng xuất',
                        style: TextStyle(color: Colors.red),
                      ),
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: Colors.red),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // App Version
                  Text(
                    'Phiên bản 1.0.0',
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Text(
          title,
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(icon, color: Colors.grey[600]),
        title: Text(title, style: const TextStyle(fontWeight: FontWeight.w500)),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.chevron_right),
        onTap: onTap,
      ),
    );
  }

  void _showComingSoon(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sắp ra mắt'),
        content: const Text(
          'Tính năng này sẽ được cập nhật trong phiên bản tiếp theo.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showEditProfileDialog(BuildContext context, user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Chỉnh sửa thông tin'),
        content: const Text(
          'Tính năng chỉnh sửa thông tin cá nhân sẽ được cập nhật trong phiên bản tiếp theo.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Đăng xuất'),
        content: const Text('Bạn có chắc chắn muốn đăng xuất?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(authProvider.notifier).logout();
            },
            child: const Text('Đăng xuất', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  /// Build theme menu item với indicator
  Widget _buildThemeMenuItem(BuildContext context, WidgetRef ref) {
    final currentTheme = ref.watch(themeProvider);

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: const Icon(Icons.palette_outlined, color: Colors.grey),
        title: const Text(
          'Giao diện',
          style: TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Text(
          '${currentTheme.displayName} - Thay đổi màu sắc ứng dụng',
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Theme color indicator
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                gradient: LinearGradient(
                  colors: [
                    currentTheme.primaryColor,
                    currentTheme.secondaryColor,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                border: Border.all(color: Colors.white, width: 2),
                boxShadow: [
                  BoxShadow(
                    color: currentTheme.primaryColor.withOpacity(0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 8),
            const Icon(Icons.chevron_right),
          ],
        ),
        onTap: () {
          context.pushNamed('theme_settings');
        },
      ),
    );
  }
}
