@echo off
echo 🎨 Shipper19+ Icon Generator
echo ================================================

echo 📁 Creating necessary directories...

REM Android directories
mkdir "android\app\src\main\res\mipmap-mdpi" 2>nul
mkdir "android\app\src\main\res\mipmap-hdpi" 2>nul
mkdir "android\app\src\main\res\mipmap-xhdpi" 2>nul
mkdir "android\app\src\main\res\mipmap-xxhdpi" 2>nul
mkdir "android\app\src\main\res\mipmap-xxxhdpi" 2>nul

REM Web directories
mkdir "web\icons" 2>nul

REM iOS directories
mkdir "ios\Runner\Assets.xcassets\AppIcon.appiconset" 2>nul

REM Windows directories
mkdir "windows\runner\resources" 2>nul

echo ✅ Directories created successfully!
echo.

echo 📋 Icon Sizes Needed:
echo.
echo 🤖 Android Icons (android/app/src/main/res/):
echo    mipmap-mdpi/ic_launcher.png     -^> 48x48px
echo    mipmap-hdpi/ic_launcher.png     -^> 72x72px
echo    mipmap-xhdpi/ic_launcher.png    -^> 96x96px
echo    mipmap-xxhdpi/ic_launcher.png   -^> 144x144px
echo    mipmap-xxxhdpi/ic_launcher.png  -^> 192x192px
echo.
echo 🌐 Web Icons (web/):
echo    favicon.png                     -^> 32x32px
echo    icons/Icon-192.png              -^> 192x192px
echo    icons/Icon-512.png              -^> 512x512px
echo    icons/Icon-maskable-192.png     -^> 192x192px
echo    icons/Icon-maskable-512.png     -^> 512x512px
echo.

echo 🛠️  Recommended Tools:
echo    1. Online: https://appicon.co/ (upload logo.png)
echo    2. Online: https://easyappicon.com/
echo    3. GIMP: File -^> Export As -^> Select size
echo.

echo ⚡ Quick Steps:
echo    1. Go to https://easyappicon.com/
echo    2. Upload assets/images/logo.png
echo    3. Download generated icons
echo    4. Replace existing icons in respective folders
echo    5. Run: flutter clean
echo    6. Run: flutter build [platform]
echo.

echo 🎉 Setup complete! Happy coding! 🚀
pause
