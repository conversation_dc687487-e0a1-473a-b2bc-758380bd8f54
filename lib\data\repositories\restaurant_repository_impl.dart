import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../core/errors/exceptions.dart';
import '../../domain/repositories/restaurant_repository.dart';
import '../datasources/local_data_source.dart';
import '../models/restaurant_model.dart';

class RestaurantRepositoryImpl implements RestaurantRepository {
  final LocalDataSource localDataSource;

  RestaurantRepositoryImpl({required this.localDataSource});

  @override
  Future<Either<Failure, List<RestaurantModel>>> getRestaurants() async {
    try {
      final restaurants = await localDataSource.getRestaurants();
      return Right(restaurants);
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(CacheFailure('Lỗi tải danh sách nhà hàng: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, RestaurantModel?>> getRestaurantById(String id) async {
    try {
      final restaurants = await localDataSource.getRestaurants();
      final restaurant = restaurants
          .where((restaurant) => restaurant.id == id)
          .firstOrNull;
      return Right(restaurant);
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(CacheFailure('Lỗi tải thông tin nhà hàng: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<RestaurantModel>>> searchRestaurants(
    String query,
  ) async {
    try {
      final restaurants = await localDataSource.getRestaurants();
      final filteredRestaurants = restaurants
          .where(
            (restaurant) =>
                restaurant.name.toLowerCase().contains(query.toLowerCase()) ||
                restaurant.description.toLowerCase().contains(
                  query.toLowerCase(),
                ),
          )
          .toList();
      return Right(filteredRestaurants);
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(CacheFailure('Lỗi tìm kiếm nhà hàng: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<RestaurantModel>>> getRestaurantsByCategory(
    String categoryId,
  ) async {
    try {
      final restaurants = await localDataSource.getRestaurants();
      final filteredRestaurants = restaurants
          .where((restaurant) => restaurant.categoryIds.contains(categoryId))
          .toList();
      return Right(filteredRestaurants);
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(
        CacheFailure('Lỗi tải nhà hàng theo danh mục: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Either<Failure, List<VoucherModel>>> getVouchers() async {
    try {
      final vouchers = await localDataSource.getVouchers();
      return Right(vouchers);
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(CacheFailure('Lỗi tải danh sách voucher: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<PostModel>>> getPosts() async {
    try {
      final posts = await localDataSource.getPosts();
      return Right(posts);
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(CacheFailure('Lỗi tải danh sách bài viết: ${e.toString()}'));
    }
  }
}
