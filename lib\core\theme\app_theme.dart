import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../constants/app_colors.dart';

/// =============================================================================
/// APP THEME CONFIGURATION - CENTRALIZED THEME MANAGEMENT
/// =============================================================================
/// 
/// Quản lý theme tập trung, dễ chỉnh sửa và bảo trì
/// Tách biệt logic theme khỏi main.dart để code sạch hơn
/// 
/// HƯỚNG DẪN SỬ DỤNG:
/// 1. Import AppTheme vào main.dart
/// 2. Sử dụng AppTheme.lightTheme trong MaterialApp
/// 3. Thay đổi theme ở đây thay vì main.dart
/// =============================================================================

class AppTheme {
  // =============================================================================
  // PRIVATE THEME COMPONENTS - Các thành phần theme riêng lẻ
  // =============================================================================
  
  /// Text Theme với Google Fonts
  static TextTheme get _textTheme => GoogleFonts.interTextTheme().copyWith(
    // Text trên nền chính (xanh lá) - màu trắng
    displayLarge: GoogleFonts.inter(color: AppColors.textOnBackground),
    displayMedium: GoogleFonts.inter(color: AppColors.textOnBackground),
    displaySmall: GoogleFonts.inter(color: AppColors.textOnBackground),
    headlineLarge: GoogleFonts.inter(color: AppColors.textOnBackground),
    headlineMedium: GoogleFonts.inter(color: AppColors.textOnBackground),
    headlineSmall: GoogleFonts.inter(color: AppColors.textOnBackground),
    titleLarge: GoogleFonts.inter(color: AppColors.textOnBackground),
    titleMedium: GoogleFonts.inter(color: AppColors.textOnBackground),
    titleSmall: GoogleFonts.inter(color: AppColors.textOnBackground),
    bodyLarge: GoogleFonts.inter(color: AppColors.textOnBackground),
    bodyMedium: GoogleFonts.inter(color: AppColors.textOnBackground),
    bodySmall: GoogleFonts.inter(color: AppColors.textSecondary),
    labelLarge: GoogleFonts.inter(color: AppColors.textOnBackground),
    labelMedium: GoogleFonts.inter(color: AppColors.textSecondary),
    labelSmall: GoogleFonts.inter(color: AppColors.textSecondary),
  );

  /// Color Scheme cho Material 3
  static ColorScheme get _colorScheme => const ColorScheme(
    brightness: Brightness.light,
    primary: AppColors.primary,
    onPrimary: AppColors.textOnBackground,
    primaryContainer: AppColors.primaryLight,
    onPrimaryContainer: AppColors.textOnSurface,
    secondary: AppColors.buttonPrimary,
    onSecondary: AppColors.buttonPrimaryText,
    secondaryContainer: AppColors.surfaceVariant,
    onSecondaryContainer: AppColors.textOnSurface,
    tertiary: AppColors.info,
    onTertiary: AppColors.textOnDark,
    tertiaryContainer: AppColors.surfaceVariant,
    onTertiaryContainer: AppColors.textOnSurface,
    error: AppColors.error,
    onError: AppColors.textOnDark,
    errorContainer: Color(0xFFFFEBEE),
    onErrorContainer: AppColors.error,
    background: AppColors.background,
    onBackground: AppColors.textOnBackground,
    surface: AppColors.surface,
    onSurface: AppColors.textOnSurface,
    surfaceVariant: AppColors.surfaceVariant,
    onSurfaceVariant: AppColors.textOnSurface,
    outline: AppColors.borderMedium,
    outlineVariant: AppColors.borderLight,
    shadow: AppColors.cardShadow,
    scrim: Color(0x80000000),
    inverseSurface: AppColors.buttonPrimary,
    onInverseSurface: AppColors.buttonPrimaryText,
    inversePrimary: AppColors.primaryDark,
    surfaceTint: AppColors.primary,
  );

  /// App Bar Theme
  static AppBarTheme get _appBarTheme => const AppBarTheme(
    centerTitle: true,
    elevation: 0,
    backgroundColor: AppColors.appBarBackground,
    foregroundColor: AppColors.appBarText,
    iconTheme: IconThemeData(color: AppColors.appBarIcon),
    titleTextStyle: TextStyle(
      color: AppColors.appBarText,
      fontSize: 20,
      fontWeight: FontWeight.w600,
    ),
  );

  /// Button Themes
  static ElevatedButtonThemeData get _elevatedButtonTheme => ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      backgroundColor: AppColors.buttonPrimary,
      foregroundColor: AppColors.buttonPrimaryText,
      disabledBackgroundColor: AppColors.buttonDisabled,
      disabledForegroundColor: AppColors.buttonTextDisabled,
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
    ),
  );

  static TextButtonThemeData get _textButtonTheme => TextButtonThemeData(
    style: TextButton.styleFrom(
      foregroundColor: AppColors.textOnBackground,
      textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
    ),
  );

  static OutlinedButtonThemeData get _outlinedButtonTheme => OutlinedButtonThemeData(
    style: OutlinedButton.styleFrom(
      foregroundColor: AppColors.textOnBackground,
      side: const BorderSide(color: AppColors.borderMedium),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    ),
  );

  /// Input Decoration Theme
  static InputDecorationTheme get _inputDecorationTheme => InputDecorationTheme(
    filled: true,
    fillColor: AppColors.inputBackground,
    hintStyle: const TextStyle(color: AppColors.inputHint),
    labelStyle: const TextStyle(color: AppColors.textOnSurface),
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: const BorderSide(color: AppColors.inputBorder),
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: const BorderSide(color: AppColors.inputBorder),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: const BorderSide(color: AppColors.inputFocusedBorder, width: 2),
    ),
    errorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: const BorderSide(color: AppColors.error),
    ),
    focusedErrorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: const BorderSide(color: AppColors.error, width: 2),
    ),
    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
  );

  /// Card Theme
  static CardThemeData get _cardTheme => CardThemeData(
    color: AppColors.cardBackground,
    elevation: 2,
    shadowColor: AppColors.cardShadow,
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
  );

  /// Tab Bar Theme
  static TabBarThemeData get _tabBarTheme => const TabBarThemeData(
    labelColor: AppColors.tabActiveText,
    unselectedLabelColor: AppColors.tabText,
    indicatorColor: AppColors.tabIndicator,
    indicatorSize: TabBarIndicatorSize.tab,
    labelStyle: TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
    unselectedLabelStyle: TextStyle(fontWeight: FontWeight.w400, fontSize: 14),
  );

  /// Bottom Navigation Theme
  static BottomNavigationBarThemeData get _bottomNavTheme => const BottomNavigationBarThemeData(
    backgroundColor: AppColors.navBackground,
    selectedItemColor: AppColors.navSelected,
    unselectedItemColor: AppColors.navUnselected,
    type: BottomNavigationBarType.fixed,
    selectedLabelStyle: TextStyle(fontWeight: FontWeight.w600),
    unselectedLabelStyle: TextStyle(fontWeight: FontWeight.w400),
  );

  /// Icon Theme
  static IconThemeData get _iconTheme => const IconThemeData(
    color: AppColors.iconOnBackground,
  );

  /// Divider Theme
  static DividerThemeData get _dividerTheme => const DividerThemeData(
    color: AppColors.divider,
    thickness: 1,
  );

  // =============================================================================
  // PUBLIC THEME - Theme chính để sử dụng trong ứng dụng
  // =============================================================================
  
  /// Light Theme cho ứng dụng (Yellow-Black theme hiện tại)
  static ThemeData get lightTheme => ThemeData(
    colorScheme: _colorScheme,
    textTheme: _textTheme,
    useMaterial3: true,
    scaffoldBackgroundColor: AppColors.background,
    appBarTheme: _appBarTheme,
    elevatedButtonTheme: _elevatedButtonTheme,
    textButtonTheme: _textButtonTheme,
    outlinedButtonTheme: _outlinedButtonTheme,
    inputDecorationTheme: _inputDecorationTheme,
    cardTheme: _cardTheme,
    tabBarTheme: _tabBarTheme,
    bottomNavigationBarTheme: _bottomNavTheme,
    iconTheme: _iconTheme,
    dividerTheme: _dividerTheme,
  );

  /// Green Theme
  static ThemeData get greenTheme => _buildThemeFromColors(
    primary: const Color(0xFF4CAF50),
    secondary: const Color(0xFF2196F3),
    background: const Color(0xFF4CAF50),
    textOnBackground: Colors.white,
    textOnSurface: Colors.black,
  );

  /// Blue Theme
  static ThemeData get blueTheme => _buildThemeFromColors(
    primary: const Color(0xFF2196F3),
    secondary: const Color(0xFF4CAF50),
    background: const Color(0xFF2196F3),
    textOnBackground: Colors.white,
    textOnSurface: Colors.black,
  );

  /// Orange Theme
  static ThemeData get orangeTheme => _buildThemeFromColors(
    primary: const Color(0xFFFF9800),
    secondary: const Color(0xFF3F51B5),
    background: const Color(0xFFFF9800),
    textOnBackground: Colors.white,
    textOnSurface: Colors.black,
  );

  /// Purple Theme
  static ThemeData get purpleTheme => _buildThemeFromColors(
    primary: const Color(0xFF9C27B0),
    secondary: const Color(0xFF00BCD4),
    background: const Color(0xFF9C27B0),
    textOnBackground: Colors.white,
    textOnSurface: Colors.black,
  );

  /// Red Theme
  static ThemeData get redTheme => _buildThemeFromColors(
    primary: const Color(0xFFF44336),
    secondary: const Color(0xFF607D8B),
    background: const Color(0xFFF44336),
    textOnBackground: Colors.white,
    textOnSurface: Colors.black,
  );

  /// Dark Theme
  static ThemeData get darkTheme => _buildThemeFromColors(
    primary: const Color(0xFF212121),
    secondary: const Color(0xFFFF5722),
    background: const Color(0xFF212121),
    textOnBackground: Colors.white,
    textOnSurface: Colors.white,
  );

  /// Helper method để build theme từ màu sắc
  static ThemeData _buildThemeFromColors({
    required Color primary,
    required Color secondary,
    required Color background,
    required Color textOnBackground,
    required Color textOnSurface,
  }) {
    return ThemeData(
      colorScheme: ColorScheme.light(
        primary: primary,
        secondary: secondary,
        background: background,
        surface: Colors.white,
        onPrimary: textOnBackground,
        onSecondary: textOnBackground,
        onBackground: textOnBackground,
        onSurface: textOnSurface,
      ),
      scaffoldBackgroundColor: background,
      useMaterial3: true,
      // Các theme components khác sẽ được tự động tính toán từ colorScheme
    );
  }
}

/// =============================================================================
/// THEME UTILITIES - Tiện ích cho theme
/// =============================================================================

extension AppThemeExtension on BuildContext {
  /// Lấy màu text phù hợp với background hiện tại
  Color get adaptiveTextColor {
    final brightness = Theme.of(this).brightness;
    return brightness == Brightness.dark 
        ? AppColors.textOnBackground 
        : AppColors.textOnSurface;
  }
  
  /// Lấy màu icon phù hợp với background hiện tại
  Color get adaptiveIconColor {
    final brightness = Theme.of(this).brightness;
    return brightness == Brightness.dark 
        ? AppColors.iconOnBackground 
        : AppColors.iconOnSurface;
  }
}

/// =============================================================================
/// QUICK COLOR REFERENCE - Tham khảo nhanh màu sắc
/// =============================================================================
/// 
/// NỀN CHÍNH: Xanh lá (#4CAF50) → Chữ trắng (#FFFFFF)
/// NỀN CARD: Trắng (#FFFFFF) → Chữ đen (#000000)  
/// INPUT: Nền xanh nhạt (#A5D6A7) → Chữ đen (#000000)
/// BUTTON: Nền xanh dương (#2196F3) → Chữ trắng (#FFFFFF)
/// TAB: Nền xanh nhạt (#81C784) → Chữ đen (#000000)
/// TAB ACTIVE: Nền xanh dương đậm (#1976D2) → Chữ trắng (#FFFFFF)
/// LOGO: Nền trắng (#FFFFFF) → Icon xanh lá (#4CAF50)
/// =============================================================================
