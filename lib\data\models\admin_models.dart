import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'admin_models.g.dart';

// Post Model
@JsonSerializable()
class PostModel extends Equatable {
  final int id;
  final String title;
  final String content;
  final String audience; // SHOP, ALL
  @JsonKey(name: 'created_by')
  final int createdBy;
  @JsonKey(name: 'is_published')
  final bool isPublished;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  const PostModel({
    required this.id,
    required this.title,
    required this.content,
    required this.audience,
    required this.createdBy,
    required this.isPublished,
    required this.createdAt,
    required this.updatedAt,
  });

  factory PostModel.fromJson(Map<String, dynamic> json) =>
      _$PostModelFromJson(json);

  Map<String, dynamic> toJson() => _$PostModelToJson(this);

  @override
  List<Object?> get props => [
        id,
        title,
        content,
        audience,
        createdBy,
        isPublished,
        createdAt,
        updatedAt,
      ];
}

// Config Models
@JsonSerializable()
class PricingConfigModel extends Equatable {
  @JsonKey(name: 'base_price')
  final double basePrice;
  @JsonKey(name: 'price_per_km')
  final double pricePerKm;
  @JsonKey(name: 'min_price')
  final double minPrice;
  @JsonKey(name: 'max_price')
  final double maxPrice;

  const PricingConfigModel({
    required this.basePrice,
    required this.pricePerKm,
    required this.minPrice,
    required this.maxPrice,
  });

  factory PricingConfigModel.fromJson(Map<String, dynamic> json) =>
      _$PricingConfigModelFromJson(json);

  Map<String, dynamic> toJson() => _$PricingConfigModelToJson(this);

  @override
  List<Object?> get props => [basePrice, pricePerKm, minPrice, maxPrice];
}

@JsonSerializable()
class SurchargeModel extends Equatable {
  final String name;
  final String type; // PERCENTAGE, FIXED
  final double value;
  @JsonKey(name: 'is_active')
  final bool isActive;

  const SurchargeModel({
    required this.name,
    required this.type,
    required this.value,
    required this.isActive,
  });

  factory SurchargeModel.fromJson(Map<String, dynamic> json) =>
      _$SurchargeModelFromJson(json);

  Map<String, dynamic> toJson() => _$SurchargeModelToJson(this);

  @override
  List<Object?> get props => [name, type, value, isActive];
}

// Admin Stats Models
@JsonSerializable()
class AdminStatsSummaryModel extends Equatable {
  @JsonKey(name: 'total_users')
  final int totalUsers;
  @JsonKey(name: 'total_shops')
  final int totalShops;
  @JsonKey(name: 'total_drivers')
  final int totalDrivers;
  @JsonKey(name: 'total_orders')
  final int totalOrders;
  @JsonKey(name: 'total_revenue')
  final double totalRevenue;
  @JsonKey(name: 'pending_orders')
  final int pendingOrders;
  @JsonKey(name: 'completed_orders')
  final int completedOrders;
  @JsonKey(name: 'cancelled_orders')
  final int cancelledOrders;

  const AdminStatsSummaryModel({
    required this.totalUsers,
    required this.totalShops,
    required this.totalDrivers,
    required this.totalOrders,
    required this.totalRevenue,
    required this.pendingOrders,
    required this.completedOrders,
    required this.cancelledOrders,
  });

  factory AdminStatsSummaryModel.fromJson(Map<String, dynamic> json) =>
      _$AdminStatsSummaryModelFromJson(json);

  Map<String, dynamic> toJson() => _$AdminStatsSummaryModelToJson(this);

  @override
  List<Object?> get props => [
        totalUsers,
        totalShops,
        totalDrivers,
        totalOrders,
        totalRevenue,
        pendingOrders,
        completedOrders,
        cancelledOrders,
      ];
}

@JsonSerializable()
class ShopStatsItemModel extends Equatable {
  @JsonKey(name: 'shop_id')
  final int shopId;
  @JsonKey(name: 'shop_name')
  final String shopName;
  @JsonKey(name: 'total_orders')
  final int totalOrders;
  @JsonKey(name: 'total_revenue')
  final double totalRevenue;
  @JsonKey(name: 'average_order_value')
  final double averageOrderValue;

  const ShopStatsItemModel({
    required this.shopId,
    required this.shopName,
    required this.totalOrders,
    required this.totalRevenue,
    required this.averageOrderValue,
  });

  factory ShopStatsItemModel.fromJson(Map<String, dynamic> json) =>
      _$ShopStatsItemModelFromJson(json);

  Map<String, dynamic> toJson() => _$ShopStatsItemModelToJson(this);

  @override
  List<Object?> get props => [
        shopId,
        shopName,
        totalOrders,
        totalRevenue,
        averageOrderValue,
      ];
}

@JsonSerializable()
class RevenueStatsModel extends Equatable {
  final String date;
  @JsonKey(name: 'total_revenue')
  final double totalRevenue;
  @JsonKey(name: 'total_orders')
  final int totalOrders;
  @JsonKey(name: 'average_order_value')
  final double averageOrderValue;

  const RevenueStatsModel({
    required this.date,
    required this.totalRevenue,
    required this.totalOrders,
    required this.averageOrderValue,
  });

  factory RevenueStatsModel.fromJson(Map<String, dynamic> json) =>
      _$RevenueStatsModelFromJson(json);

  Map<String, dynamic> toJson() => _$RevenueStatsModelToJson(this);

  @override
  List<Object?> get props => [
        date,
        totalRevenue,
        totalOrders,
        averageOrderValue,
      ];
}
