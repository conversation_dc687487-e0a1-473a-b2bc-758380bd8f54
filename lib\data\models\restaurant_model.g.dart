// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'restaurant_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RestaurantModel _$RestaurantModelFromJson(Map<String, dynamic> json) =>
    RestaurantModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      address: json['address'] as String,
      latitude: _doubleFromString(json['latitude']),
      longitude: _doubleFromString(json['longitude']),
      phone: json['phone'] as String?,
      email: json['email'] as String?,
      image: json['image'] as String?,
      images:
          (json['images'] as List<dynamic>).map((e) => e as String).toList(),
      rating: _doubleFromString(json['rating']),
      reviewCount: (json['reviewCount'] as num).toInt(),
      businessHours: json['businessHours'] as String?,
      isOpen: json['isOpen'] as bool,
      isActive: json['isActive'] as bool,
      estimatedDeliveryTime: (json['estimatedDeliveryTime'] as num).toInt(),
      deliveryFee: _doubleFromString(json['deliveryFee']),
      minimumOrder: _doubleFromString(json['minimumOrder']),
      categoryIds: (json['categoryIds'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$RestaurantModelToJson(RestaurantModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'address': instance.address,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'phone': instance.phone,
      'email': instance.email,
      'image': instance.image,
      'images': instance.images,
      'rating': instance.rating,
      'reviewCount': instance.reviewCount,
      'businessHours': instance.businessHours,
      'isOpen': instance.isOpen,
      'isActive': instance.isActive,
      'estimatedDeliveryTime': instance.estimatedDeliveryTime,
      'deliveryFee': instance.deliveryFee,
      'minimumOrder': instance.minimumOrder,
      'categoryIds': instance.categoryIds,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

VoucherModel _$VoucherModelFromJson(Map<String, dynamic> json) => VoucherModel(
      id: json['id'] as String,
      code: json['code'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: json['type'] as String,
      value: _doubleFromString(json['value']),
      minimumOrder: _nullableDoubleFromString(json['minimumOrder']),
      maximumDiscount: _nullableDoubleFromString(json['maximumDiscount']),
      validFrom: DateTime.parse(json['validFrom'] as String),
      validTo: DateTime.parse(json['validTo'] as String),
      usageLimit: (json['usageLimit'] as num?)?.toInt(),
      usedCount: (json['usedCount'] as num).toInt(),
      isActive: json['isActive'] as bool,
      applicableRestaurants: (json['applicableRestaurants'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      applicableCategories: (json['applicableCategories'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      userType: json['userType'] as String?,
    );

Map<String, dynamic> _$VoucherModelToJson(VoucherModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'code': instance.code,
      'title': instance.title,
      'description': instance.description,
      'type': instance.type,
      'value': instance.value,
      'minimumOrder': instance.minimumOrder,
      'maximumDiscount': instance.maximumDiscount,
      'validFrom': instance.validFrom.toIso8601String(),
      'validTo': instance.validTo.toIso8601String(),
      'usageLimit': instance.usageLimit,
      'usedCount': instance.usedCount,
      'isActive': instance.isActive,
      'applicableRestaurants': instance.applicableRestaurants,
      'applicableCategories': instance.applicableCategories,
      'userType': instance.userType,
    };

PostModel _$PostModelFromJson(Map<String, dynamic> json) => PostModel(
      id: json['id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      image: json['image'] as String?,
      audience: json['audience'] as String,
      createdBy: json['createdBy'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isActive: json['isActive'] as bool,
    );

Map<String, dynamic> _$PostModelToJson(PostModel instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'content': instance.content,
      'image': instance.image,
      'audience': instance.audience,
      'createdBy': instance.createdBy,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isActive': instance.isActive,
    };
