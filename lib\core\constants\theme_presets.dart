import 'package:flutter/material.dart';

/// =============================================================================
/// THEME PRESETS - Các bộ màu có sẵn
/// =============================================================================
/// 
/// Chứa các bộ màu đã được thiết kế sẵn, chỉ cần uncomment để sử dụng
/// Hoặc copy vào app_colors.dart để áp dụng
/// 
/// CÁCH SỬ DỤNG:
/// 1. Chọn một preset bên dưới
/// 2. Copy các giá trị BASE_COLORS 
/// 3. Paste vào app_colors.dart thay thế các giá trị hiện tại
/// 4. Hot restart ứng dụng
/// =============================================================================

class ThemePresets {
  
  // =============================================================================
  // YELLOW-BLACK THEME (HIỆN TẠI) - Theme vàng-đen
  // =============================================================================
  static const Map<String, Color> yellowBlackTheme = {
    'primaryBase': Color(0xFFFFEB3B),      // Vàng sáng
    'primaryDark': Color(0xFFFBC02D),      // Vàng đậm
    'primaryLight': Color(0xFFFFE082),     // Vàng nhạt
    'primaryVeryLight': Color(0xFFFFF9C4), // Vàng rất nhạt
    'secondaryBase': Color(0xFF070000),    // Đen button
    'secondaryDark': Color(0xFF225A33),    // Xanh lá đậm (tab active)
    'secondaryLight': Color(0xFF424242),   // Xám đậm
  };

  // =============================================================================
  // GREEN THEME - Theme xanh lá (cũ)
  // =============================================================================
  static const Map<String, Color> greenTheme = {
    'primaryBase': Color(0xFF4CAF50),      // Xanh lá
    'primaryDark': Color(0xFF388E3C),      // Xanh lá đậm
    'primaryLight': Color(0xFF81C784),     // Xanh lá nhạt
    'primaryVeryLight': Color(0xFFA5D6A7), // Xanh lá rất nhạt
    'secondaryBase': Color(0xFF2196F3),    // Xanh dương
    'secondaryDark': Color(0xFF1976D2),    // Xanh dương đậm
    'secondaryLight': Color(0xFF90CAF9),   // Xanh dương nhạt
  };
  
  // =============================================================================
  // BLUE THEME - Theme xanh dương
  // =============================================================================
  static const Map<String, Color> blueTheme = {
    'primaryBase': Color(0xFF2196F3),      // Xanh dương
    'primaryDark': Color(0xFF1976D2),      // Xanh dương đậm
    'primaryLight': Color(0xFF90CAF9),     // Xanh dương nhạt
    'primaryVeryLight': Color(0xFFBBDEFB), // Xanh dương rất nhạt
    'secondaryBase': Color(0xFF4CAF50),    // Xanh lá
    'secondaryDark': Color(0xFF388E3C),    // Xanh lá đậm
    'secondaryLight': Color(0xFF81C784),   // Xanh lá nhạt
  };
  
  // =============================================================================
  // ORANGE THEME - Theme cam
  // =============================================================================
  static const Map<String, Color> orangeTheme = {
    'primaryBase': Color(0xFFFF9800),      // Cam
    'primaryDark': Color(0xFFF57C00),      // Cam đậm
    'primaryLight': Color(0xFFFFB74D),     // Cam nhạt
    'primaryVeryLight': Color(0xFFFFE0B2), // Cam rất nhạt
    'secondaryBase': Color(0xFF3F51B5),    // Tím
    'secondaryDark': Color(0xFF303F9F),    // Tím đậm
    'secondaryLight': Color(0xFF9FA8DA),   // Tím nhạt
  };
  
  // =============================================================================
  // PURPLE THEME - Theme tím
  // =============================================================================
  static const Map<String, Color> purpleTheme = {
    'primaryBase': Color(0xFF9C27B0),      // Tím
    'primaryDark': Color(0xFF7B1FA2),      // Tím đậm
    'primaryLight': Color(0xFFBA68C8),     // Tím nhạt
    'primaryVeryLight': Color(0xFFE1BEE7), // Tím rất nhạt
    'secondaryBase': Color(0xFF00BCD4),    // Cyan
    'secondaryDark': Color(0xFF0097A7),    // Cyan đậm
    'secondaryLight': Color(0xFF4DD0E1),   // Cyan nhạt
  };
  
  // =============================================================================
  // RED THEME - Theme đỏ
  // =============================================================================
  static const Map<String, Color> redTheme = {
    'primaryBase': Color(0xFFF44336),      // Đỏ
    'primaryDark': Color(0xFFD32F2F),      // Đỏ đậm
    'primaryLight': Color(0xFFEF5350),     // Đỏ nhạt
    'primaryVeryLight': Color(0xFFFFCDD2), // Đỏ rất nhạt
    'secondaryBase': Color(0xFF607D8B),    // Xanh xám
    'secondaryDark': Color(0xFF455A64),    // Xanh xám đậm
    'secondaryLight': Color(0xFF90A4AE),   // Xanh xám nhạt
  };
  
  // =============================================================================
  // DARK THEME - Theme tối
  // =============================================================================
  static const Map<String, Color> darkTheme = {
    'primaryBase': Color(0xFF212121),      // Xám đen
    'primaryDark': Color(0xFF000000),      // Đen
    'primaryLight': Color(0xFF424242),     // Xám
    'primaryVeryLight': Color(0xFF616161), // Xám nhạt
    'secondaryBase': Color(0xFFFF5722),    // Cam đỏ
    'secondaryDark': Color(0xFFE64A19),    // Cam đỏ đậm
    'secondaryLight': Color(0xFFFF8A65),   // Cam đỏ nhạt
  };
  
  // =============================================================================
  // HELPER METHODS - Phương thức hỗ trợ
  // =============================================================================
  
  /// Lấy tất cả theme presets
  static Map<String, Map<String, Color>> get allThemes => {
    'YellowBlack': yellowBlackTheme, // Theme hiện tại
    'Green': greenTheme,
    'Blue': blueTheme,
    'Orange': orangeTheme,
    'Purple': purpleTheme,
    'Red': redTheme,
    'Dark': darkTheme,
  };
  
  /// Tạo code để copy-paste vào app_colors.dart
  static String generateThemeCode(String themeName) {
    final theme = allThemes[themeName];
    if (theme == null) return 'Theme not found';
    
    return '''
// Generated ${themeName} Theme
static const Color _primaryBase = Color(0x${theme['primaryBase']!.value.toRadixString(16).toUpperCase()});
static const Color _primaryDark = Color(0x${theme['primaryDark']!.value.toRadixString(16).toUpperCase()});
static const Color _primaryLight = Color(0x${theme['primaryLight']!.value.toRadixString(16).toUpperCase()});
static const Color _primaryVeryLight = Color(0x${theme['primaryVeryLight']!.value.toRadixString(16).toUpperCase()});
static const Color _secondaryBase = Color(0x${theme['secondaryBase']!.value.toRadixString(16).toUpperCase()});
static const Color _secondaryDark = Color(0x${theme['secondaryDark']!.value.toRadixString(16).toUpperCase()});
static const Color _secondaryLight = Color(0x${theme['secondaryLight']!.value.toRadixString(16).toUpperCase()});
''';
  }
}

/// =============================================================================
/// QUICK SWITCH UTILITY - Tiện ích chuyển theme nhanh
/// =============================================================================

class QuickThemeSwitch {
  /// Chuyển đổi theme nhanh (để test)
  static void switchToTheme(String themeName) {
    print('=== THEME SWITCH GUIDE ===');
    print('Copy code below to app_colors.dart:');
    print(ThemePresets.generateThemeCode(themeName));
    print('Then hot restart the app!');
  }
}

/// =============================================================================
/// USAGE EXAMPLES - Ví dụ sử dụng
/// =============================================================================
/// 
/// // Chuyển sang Blue Theme:
/// QuickThemeSwitch.switchToTheme('Blue');
/// 
/// // Lấy màu từ preset:
/// Color myColor = ThemePresets.blueTheme['primaryBase']!;
/// 
/// // Tạo theme tùy chỉnh:
/// static const Map<String, Color> myCustomTheme = {
///   'primaryBase': Color(0xFF123456),
///   'secondaryBase': Color(0xFF654321),
///   // ...
/// };
/// =============================================================================
