// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shop_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ShopModel _$ShopModelFromJson(Map<String, dynamic> json) => ShopModel(
      id: _intFromAny(json['id']),
      userId: _nullableIntFromAny(json['user_id']),
      name: json['name'] as String,
      description: json['description'] as String?,
      address: json['address'] as String?,
      contact: json['contact'] as String?,
      phone: json['phone'] as String?,
      email: json['email'] as String?,
      image: json['image'] as String?,
      images:
          (json['images'] as List<dynamic>?)?.map((e) => e as String).toList(),
      latitude: _nullableDoubleFromString(json['latitude']),
      longitude: _nullableDoubleFromString(json['longitude']),
      isActive: _boolFromAny(json['is_active']),
      rating: _nullableDoubleFromString(json['rating']),
      reviewCount: _nullableIntFromAny(json['review_count']),
      businessHours: json['business_hours'] as String?,
      isOpen: _nullableBoolFromAny(json['is_open']),
      estimatedDeliveryTime:
          _nullableIntFromAny(json['estimated_delivery_time']),
      deliveryFee: _nullableDoubleFromString(json['delivery_fee']),
      minimumOrder: _nullableDoubleFromString(json['minimum_order']),
      categoryIds: _intListFromAny(json['category_ids']),
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$ShopModelToJson(ShopModel instance) => <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'name': instance.name,
      'description': instance.description,
      'address': instance.address,
      'contact': instance.contact,
      'phone': instance.phone,
      'email': instance.email,
      'image': instance.image,
      'images': instance.images,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'is_active': instance.isActive,
      'rating': instance.rating,
      'review_count': instance.reviewCount,
      'business_hours': instance.businessHours,
      'is_open': instance.isOpen,
      'estimated_delivery_time': instance.estimatedDeliveryTime,
      'delivery_fee': instance.deliveryFee,
      'minimum_order': instance.minimumOrder,
      'category_ids': instance.categoryIds,
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
    };

CategoryModel _$CategoryModelFromJson(Map<String, dynamic> json) =>
    CategoryModel(
      id: _intFromAny(json['id']),
      name: json['name'] as String,
      description: json['description'] as String?,
      icon: json['icon'] as String?,
    );

Map<String, dynamic> _$CategoryModelToJson(CategoryModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'icon': instance.icon,
    };

ShopStatsModel _$ShopStatsModelFromJson(Map<String, dynamic> json) =>
    ShopStatsModel(
      totalOrders: (json['total_orders'] as num).toInt(),
      totalRevenue: _doubleFromString(json['total_revenue']),
      completedOrders: (json['completed_orders'] as num).toInt(),
      pendingOrders: (json['pending_orders'] as num).toInt(),
      cancelledOrders: (json['cancelled_orders'] as num).toInt(),
      averageRating: _doubleFromString(json['average_rating']),
      dailyStats: (json['daily_stats'] as List<dynamic>?)
          ?.map((e) => DailyStatsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$ShopStatsModelToJson(ShopStatsModel instance) =>
    <String, dynamic>{
      'total_orders': instance.totalOrders,
      'total_revenue': instance.totalRevenue,
      'completed_orders': instance.completedOrders,
      'pending_orders': instance.pendingOrders,
      'cancelled_orders': instance.cancelledOrders,
      'average_rating': instance.averageRating,
      'daily_stats': instance.dailyStats,
    };

DailyStatsModel _$DailyStatsModelFromJson(Map<String, dynamic> json) =>
    DailyStatsModel(
      date: json['date'] as String,
      orders: (json['orders'] as num).toInt(),
      revenue: _doubleFromString(json['revenue']),
    );

Map<String, dynamic> _$DailyStatsModelToJson(DailyStatsModel instance) =>
    <String, dynamic>{
      'date': instance.date,
      'orders': instance.orders,
      'revenue': instance.revenue,
    };
