import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../data/models/order_model.dart';

abstract class OrderRepository {
  Future<Either<Failure, List<OrderModel>>> getOrders();
  Future<Either<Failure, OrderModel?>> getOrderById(String id);
  Future<Either<Failure, List<OrderModel>>> getOrdersByCustomer(String customerId);
  Future<Either<Failure, List<OrderModel>>> getOrdersByShop(String shopId);
  Future<Either<Failure, List<OrderModel>>> getOrdersByDriver(String driverId);
  Future<Either<Failure, List<OrderModel>>> getAvailableOrders();
  Future<Either<Failure, OrderModel>> createOrder(OrderModel order);
  Future<Either<Failure, OrderModel>> updateOrderStatus(String orderId, String status);
  Future<Either<Failure, OrderModel>> assignDriver(String orderId, String driverId);
}
