# PowerShell script để tạo app icons từ logo.png
# Yêu cầu: ImageMagick hoặc sử dụng online converter

param(
    [string]$LogoPath = "assets\images\logo.png"
)

Write-Host "🎨 Shipper19+ Icon Generator (PowerShell)" -ForegroundColor Cyan
Write-Host "=" * 50

# Kiểm tra logo có tồn tại không
if (-not (Test-Path $LogoPath)) {
    Write-Host "❌ Logo not found: $LogoPath" -ForegroundColor Red
    Write-Host "Please make sure logo.png exists in assets/images/" -ForegroundColor Yellow
    exit 1
}

Write-Host "🖼️  Logo path: $LogoPath" -ForegroundColor Green

# Function để tạo thư mục
function New-DirectoryIfNotExists {
    param([string]$Path)
    if (-not (Test-Path $Path)) {
        New-Item -ItemType Directory -Path $Path -Force | Out-Null
        Write-Host "📁 Created directory: $Path" -ForegroundColor Blue
    }
}

# Hướng dẫn thủ công vì PowerShell không có built-in image processing
Write-Host ""
Write-Host "📋 Manual Steps (since PowerShell doesn't have built-in image processing):" -ForegroundColor Yellow
Write-Host ""

Write-Host "🤖 Android Icons (android/app/src/main/res/):" -ForegroundColor Cyan
Write-Host "   mipmap-mdpi/ic_launcher.png     -> 48x48px"
Write-Host "   mipmap-hdpi/ic_launcher.png     -> 72x72px"
Write-Host "   mipmap-xhdpi/ic_launcher.png    -> 96x96px"
Write-Host "   mipmap-xxhdpi/ic_launcher.png   -> 144x144px"
Write-Host "   mipmap-xxxhdpi/ic_launcher.png  -> 192x192px"
Write-Host ""

Write-Host "🌐 Web Icons (web/):" -ForegroundColor Cyan
Write-Host "   favicon.png                     -> 32x32px"
Write-Host "   icons/Icon-192.png              -> 192x192px"
Write-Host "   icons/Icon-512.png              -> 512x512px"
Write-Host "   icons/Icon-maskable-192.png     -> 192x192px"
Write-Host "   icons/Icon-maskable-512.png     -> 512x512px"
Write-Host ""

Write-Host "🍎 iOS Icons (ios/Runner/Assets.xcassets/AppIcon.appiconset/):" -ForegroundColor Cyan
Write-Host "   Icon-App-20pt.png               -> 20x20px"
Write-Host "   Icon-App-29pt.png               -> 29x29px"
Write-Host "   Icon-App-40pt.png               -> 40x40px"
Write-Host "   Icon-App-60pt.png               -> 60x60px"
Write-Host "   Icon-App-76pt.png               -> 76x76px"
Write-Host "   Icon-App-83.5pt.png             -> 83x83px"
Write-Host "   Icon-App-1024pt.png             -> 1024x1024px"
Write-Host ""

Write-Host "🪟 Windows Icons (windows/runner/resources/):" -ForegroundColor Cyan
Write-Host "   app_icon_16.png                 -> 16x16px"
Write-Host "   app_icon_32.png                 -> 32x32px"
Write-Host "   app_icon_48.png                 -> 48x48px"
Write-Host "   app_icon_256.png                -> 256x256px"
Write-Host ""

Write-Host "🛠️  Recommended Tools:" -ForegroundColor Green
Write-Host "   1. Online: https://appicon.co/ (upload logo.png)"
Write-Host "   2. ImageMagick: magick convert logo.png -resize 48x48 ic_launcher.png"
Write-Host "   3. GIMP: File -> Export As -> Select size"
Write-Host "   4. Photoshop: Image -> Image Size"
Write-Host ""

Write-Host "⚡ Quick Online Solution:" -ForegroundColor Yellow
Write-Host "   1. Go to https://easyappicon.com/"
Write-Host "   2. Upload assets/images/logo.png"
Write-Host "   3. Download generated icons"
Write-Host "   4. Replace existing icons in respective folders"
Write-Host ""

# Tạo các thư mục cần thiết
Write-Host "📁 Creating necessary directories..." -ForegroundColor Blue

# Android directories
$androidDirs = @(
    "android\app\src\main\res\mipmap-mdpi",
    "android\app\src\main\res\mipmap-hdpi", 
    "android\app\src\main\res\mipmap-xhdpi",
    "android\app\src\main\res\mipmap-xxhdpi",
    "android\app\src\main\res\mipmap-xxxhdpi"
)

foreach ($dir in $androidDirs) {
    New-DirectoryIfNotExists $dir
}

# Web directories
New-DirectoryIfNotExists "web\icons"

# iOS directories
New-DirectoryIfNotExists "ios\Runner\Assets.xcassets\AppIcon.appiconset"

# Windows directories
New-DirectoryIfNotExists "windows\runner\resources"

Write-Host ""
Write-Host "✅ Directories created successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📝 Next steps:" -ForegroundColor Cyan
Write-Host "   1. Use one of the recommended tools to resize logo.png"
Write-Host "   2. Replace the existing icon files with new ones"
Write-Host "   3. Run: flutter clean"
Write-Host "   4. Run: flutter build [platform]"
Write-Host "   5. Test the app on different devices"
Write-Host ""
Write-Host "🎉 Setup complete! Happy coding! 🚀" -ForegroundColor Green
