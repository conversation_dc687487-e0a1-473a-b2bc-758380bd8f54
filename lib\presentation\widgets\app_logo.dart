import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';

/// =============================================================================
/// APP LOGO WIDGET - Widget logo tái sử dụng
/// =============================================================================
/// 
/// Widget logo thống nhất cho toàn bộ ứng dụng
/// Tự động fallback về icon nếu không load được ảnh
/// 
/// CÁCH SỬ DỤNG:
/// AppLogo() - <PERSON><PERSON> kích thước mặc định
/// AppLogo.large() - Logo lớn cho splash screen
/// AppLogo.small() - Logo nhỏ cho app bar
/// AppLogo.custom(size: 100) - <PERSON>go kích thước tùy chỉnh
/// =============================================================================

class AppLogo extends StatelessWidget {
  final double size;
  final bool showBackground;
  final Color? backgroundColor;
  final double borderRadius;
  final EdgeInsetsGeometry? padding;

  const AppLogo({
    super.key,
    this.size = 60,
    this.showBackground = false, // Không có nền theo yêu cầu
    this.backgroundColor,
    this.borderRadius = 12,
    this.padding,
  });

  /// Logo lớn cho splash screen
  const AppLogo.large({
    super.key,
    this.size = 100,
    this.showBackground = false, // Không có nền
    this.backgroundColor,
    this.borderRadius = 16,
    this.padding,
  });

  /// Logo nhỏ cho app bar
  const AppLogo.small({
    super.key,
    this.size = 40,
    this.showBackground = false, // Không có nền
    this.backgroundColor,
    this.borderRadius = 8,
    this.padding,
  });

  /// Logo kích thước tùy chỉnh
  const AppLogo.custom({
    super.key,
    required this.size,
    this.showBackground = false, // Không có nền theo yêu cầu
    this.backgroundColor,
    this.borderRadius = 12,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    Widget logoWidget = Image.asset(
      'assets/images/logo.png',
      width: size,
      height: size,
      fit: BoxFit.contain,
      errorBuilder: (context, error, stackTrace) {
        // Fallback to icon if image fails to load
        return Icon(
          Icons.delivery_dining,
          size: size * 0.8, // Icon nhỏ hơn một chút so với container
          color: AppColors.iconOnBackground, // Luôn dùng màu icon trên nền
        );
      },
    );

    if (showBackground) {
      return Container(
        padding: padding ?? EdgeInsets.all(size * 0.15),
        decoration: BoxDecoration(
          color: backgroundColor ?? AppColors.logoBackground,
          borderRadius: BorderRadius.circular(borderRadius),
          boxShadow: [
            BoxShadow(
              color: AppColors.cardShadow,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(borderRadius * 0.6),
          child: logoWidget,
        ),
      );
    }

    return ClipRRect(
      borderRadius: BorderRadius.circular(borderRadius),
      child: logoWidget,
    );
  }
}

/// =============================================================================
/// LOGO VARIANTS - Các biến thể logo
/// =============================================================================

/// Logo cho splash screen với animation
class AnimatedAppLogo extends StatefulWidget {
  final double size;
  final Duration animationDuration;

  const AnimatedAppLogo({
    super.key,
    this.size = 100,
    this.animationDuration = const Duration(milliseconds: 1500),
  });

  @override
  State<AnimatedAppLogo> createState() => _AnimatedAppLogoState();
}

class _AnimatedAppLogoState extends State<AnimatedAppLogo>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeIn,
    ));

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: AppLogo.large(size: widget.size),
          ),
        );
      },
    );
  }
}

/// Logo cho app bar với hiệu ứng hover
class AppBarLogo extends StatefulWidget {
  final VoidCallback? onTap;

  const AppBarLogo({
    super.key,
    this.onTap,
  });

  @override
  State<AppBarLogo> createState() => _AppBarLogoState();
}

class _AppBarLogoState extends State<AppBarLogo> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: GestureDetector(
        onTap: widget.onTap,
        child: AnimatedScale(
          scale: _isHovered ? 1.1 : 1.0,
          duration: const Duration(milliseconds: 200),
          child: const AppLogo.small(),
        ),
      ),
    );
  }
}

/// =============================================================================
/// USAGE EXAMPLES - Ví dụ sử dụng
/// =============================================================================
/// 
/// // Logo cơ bản
/// AppLogo()
/// 
/// // Logo lớn cho splash
/// AppLogo.large()
/// 
/// // Logo nhỏ cho app bar
/// AppLogo.small()
/// 
/// // Logo tùy chỉnh
/// AppLogo.custom(size: 120, borderRadius: 20)
/// 
/// // Logo có animation cho splash
/// AnimatedAppLogo()
/// 
/// // Logo cho app bar với hover effect
/// AppBarLogo(onTap: () => print('Logo tapped'))
/// =============================================================================
