// Example file showing how to use the API services
// This file is for documentation purposes and should not be imported in production code

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../data/datasources/api_service_provider.dart';
import '../data/models/auth_response_model.dart';
import '../data/models/user_model.dart';
import '../data/models/order_model.dart';
import '../data/models/shop_model.dart';
import '../data/models/product_model.dart';

class ApiUsageExamples {
  // ============================================================================
  // AUTH API EXAMPLES
  // ============================================================================

  /// Example: Register a new user
  static Future<void> registerExample(WidgetRef ref) async {
    final authService = ref.read(authApiServiceProvider);

    try {
      final response = await authService.register({
        'name': 'John Doe',
        'email': '<EMAIL>',
        'phone': '**********',
        'password': 'password123',
        'password_confirmation': 'password123',
        'role': 'CUSTOMER', // CUSTOMER, SHOP, SHIPPER
      });
      if (response.success && response.data != null) {
        final authData = AuthResponseModel.fromJson(response.data);
        print('User registered: ${authData.user.name}');
        print('Token: ${authData.token}');
      }
    } catch (e) {
      print('Error: $e');
    }
  }

  /// Example: Login user
  static Future<void> loginExample(WidgetRef ref) async {
    final authService = ref.read(authApiServiceProvider);

    try {
      final response = await authService.login({
        'email': '<EMAIL>',
        'password': 'password123',
      });
      if (response.success && response.data != null) {
        final authData = AuthResponseModel.fromJson(response.data);
        print('Login successful: ${authData.user.name}');
        print('Token: ${authData.token}');
      }
    } catch (e) {
      print('Error: $e');
    }
  }

  /// Example: Get current user
  static Future<void> getCurrentUserExample(WidgetRef ref) async {
    final authService = ref.read(authApiServiceProvider);

    try {
      final response = await authService.getCurrentUser();

      if (response.success) {
        final user = UserModel.fromJson(response.data);
        print('Current user: ${user.name} (${user.role})');
      } else {
        print('Failed to get user: ${response.message}');
      }
    } catch (e) {
      print('Error: $e');
    }
  }

  // ============================================================================
  // CUSTOMER API EXAMPLES
  // ============================================================================

  /// Example: Get shops list
  static Future<void> getShopsExample(WidgetRef ref) async {
    final customerService = ref.read(customerApiServiceProvider);

    try {
      final response = await customerService.getShops(
        1, // page
        10, // per_page
        'pizza', // search
        null, // category_id
      );

      if (response.success && response.data != null) {
        final shops = (response.data!.data as List)
            .map((json) => ShopModel.fromJson(json))
            .toList();
        print('Found ${shops.length} shops');
        for (final shop in shops) {
          print('- ${shop.name}: ${shop.address}');
        }
      }
    } catch (e) {
      print('Error: $e');
    }
  }

  /// Example: Create order
  static Future<void> createOrderExample(WidgetRef ref) async {
    final customerService = ref.read(customerApiServiceProvider);

    try {
      final response = await customerService.createOrder({
        'shop_id': 1,
        'items': [
          {'product_id': 1, 'quantity': 2, 'note': 'Extra cheese'},
        ],
        'pickup_address': {
          'address': '123 Shop Street',
          'latitude': 10.762622,
          'longitude': 106.660172,
          'contact_name': 'Shop Owner',
          'contact_phone': '**********',
        },
        'dropoff_address': {
          'address': '456 Customer Street',
          'latitude': 10.787272,
          'longitude': 106.749810,
          'contact_name': 'Customer Name',
          'contact_phone': '**********',
        },
        'note': 'Please call when arrived',
        'payment_method': 'CASH',
      });

      if (response.success) {
        final order = OrderModel.fromJson(response.data);
        print('Order created: ${order.id}');
      }
    } catch (e) {
      print('Error: $e');
    }
  }

  // ============================================================================
  // SHOP API EXAMPLES
  // ============================================================================

  /// Example: Get shop profile
  static Future<void> getShopProfileExample(WidgetRef ref) async {
    final shopService = ref.read(shopApiServiceProvider);

    try {
      final response = await shopService.getProfile();

      if (response.success) {
        final shop = ShopModel.fromJson(response.data);
        print('Shop: ${shop.name}');
        print('Address: ${shop.address}');
        print('Rating: ${shop.rating}');
      }
    } catch (e) {
      print('Error: $e');
    }
  }

  /// Example: Create product
  static Future<void> createProductExample(WidgetRef ref) async {
    final shopService = ref.read(shopApiServiceProvider);

    try {
      final response = await shopService.createProduct({
        'name': 'Margherita Pizza',
        'description': 'Classic pizza with tomato and mozzarella',
        'price': 150000,
        'category_id': 1,
        'is_available': true,
        'images': ['https://example.com/pizza.jpg'],
      });

      if (response.success) {
        final product = ProductModel.fromJson(response.data);
        print('Product created: ${product.name}');
      }
    } catch (e) {
      print('Error: $e');
    }
  }

  // ============================================================================
  // DRIVER API EXAMPLES
  // ============================================================================

  /// Example: Get available orders
  static Future<void> getAvailableOrdersExample(WidgetRef ref) async {
    final driverService = ref.read(driverApiServiceProvider);

    try {
      final response = await driverService.getAvailableOrders(
        1, // page
        20, // per_page
        10.762622, // pickup_latitude
        106.660172, // pickup_longitude
        5.0, // max_distance in km
      );

      if (response.success) {
        final orders = (response.data as List)
            .map((json) => OrderModel.fromJson(json))
            .toList();
        print('Available orders: ${orders.length}');
        for (final order in orders) {
          print('- Order ${order.id}: ${order.pickupAddress.address}');
        }
      }
    } catch (e) {
      print('Error: $e');
    }
  }

  /// Example: Accept order
  static Future<void> acceptOrderExample(WidgetRef ref) async {
    final driverService = ref.read(driverApiServiceProvider);

    try {
      final response = await driverService.acceptOrder('order-id-123');

      if (response.success) {
        print('Order accepted successfully');
      } else {
        print('Failed to accept order: ${response.message}');
      }
    } catch (e) {
      print('Error: $e');
    }
  }

  /// Example: Update order status
  static Future<void> updateOrderStatusExample(WidgetRef ref) async {
    final driverService = ref.read(driverApiServiceProvider);

    try {
      final response = await driverService.updateOrderStatus('order-id-123', {
        'status': 'PICKING',
        'lat': 10.762622,
        'long': 106.660172,
      });

      if (response.success) {
        print('Order status updated successfully');
      }
    } catch (e) {
      print('Error: $e');
    }
  }

  // ============================================================================
  // ADMIN API EXAMPLES
  // ============================================================================

  /// Example: Get users list
  static Future<void> getUsersExample(WidgetRef ref) async {
    final adminService = ref.read(adminApiServiceProvider);

    try {
      final response = await adminService.getUsers(
        1, // page
        20, // per_page
        'john', // search
        'CUSTOMER', // role filter
      );

      if (response.success && response.data != null) {
        final users = (response.data!.data as List)
            .map((json) => UserModel.fromJson(json))
            .toList();
        print('Found ${users.length} users');
      }
    } catch (e) {
      print('Error: $e');
    }
  }

  /// Example: Get admin stats
  static Future<void> getAdminStatsExample(WidgetRef ref) async {
    final adminService = ref.read(adminApiServiceProvider);

    try {
      final response = await adminService.getStatsSummary(
        '2024-01-01', // date_from
        '2024-12-31', // date_to
      );

      if (response.success) {
        print('Stats retrieved successfully');
        print('Data: ${response.data}');
      }
    } catch (e) {
      print('Error: $e');
    }
  }
}
