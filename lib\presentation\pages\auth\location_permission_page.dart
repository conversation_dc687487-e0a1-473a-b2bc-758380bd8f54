import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/services/location_service.dart';
import '../../../core/constants/app_colors.dart';
import '../../widgets/app_logo.dart';

class LocationPermissionPage extends ConsumerStatefulWidget {
  const LocationPermissionPage({super.key});

  @override
  ConsumerState<LocationPermissionPage> createState() => _LocationPermissionPageState();
}

class _LocationPermissionPageState extends ConsumerState<LocationPermissionPage> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              const SizedBox(height: 60),
              
              // Logo
              const Center(
                child: AppLogo.large(),
              ),
              const SizedBox(height: 40),

              // Location Icon
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.location_on,
                  size: 60,
                  color: AppColors.primary,
                ),
              ),
              const SizedBox(height: 40),

              // Title
              Text(
                'Cho phép truy cập vị trí',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),

              // Description
              Text(
                'Chúng tôi cần truy cập vị trí của bạn để:\n\n'
                '• Tìm nhà hàng gần bạn\n'
                '• Tính toán phí giao hàng chính xác\n'
                '• Theo dõi đơn hàng của bạn\n'
                '• Cung cấp trải nghiệm tốt nhất',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.grey[600],
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              
              const Spacer(),

              // Allow Location Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _requestLocationPermission,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text(
                          'Cho phép truy cập vị trí',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                ),
              ),
              const SizedBox(height: 12),

              // Skip Button
              SizedBox(
                width: double.infinity,
                child: TextButton(
                  onPressed: _isLoading ? null : _skipLocationPermission,
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: Text(
                    'Bỏ qua (sử dụng vị trí mặc định)',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _requestLocationPermission() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final status = await LocationPermissionHelper.getPermissionStatus();
      
      switch (status) {
        case LocationPermissionStatus.granted:
          _navigateToHome();
          break;
          
        case LocationPermissionStatus.denied:
          final granted = await LocationPermissionHelper.requestLocationPermission();
          if (granted) {
            _navigateToHome();
          } else {
            _showPermissionDeniedDialog();
          }
          break;
          
        case LocationPermissionStatus.deniedForever:
          _showPermissionDeniedForeverDialog();
          break;
          
        case LocationPermissionStatus.serviceDisabled:
          _showLocationServiceDisabledDialog();
          break;
      }
    } catch (e) {
      _showErrorDialog('Có lỗi xảy ra khi yêu cầu quyền truy cập vị trí');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _skipLocationPermission() {
    _navigateToHome();
  }

  void _navigateToHome() {
    context.go('/customer');
  }

  void _showPermissionDeniedDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Quyền truy cập vị trí bị từ chối'),
        content: const Text(
          'Bạn có thể bật quyền truy cập vị trí trong cài đặt để có trải nghiệm tốt hơn.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToHome();
            },
            child: const Text('Tiếp tục'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _requestLocationPermission();
            },
            child: const Text('Thử lại'),
          ),
        ],
      ),
    );
  }

  void _showPermissionDeniedForeverDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cần quyền truy cập vị trí'),
        content: const Text(
          'Vui lòng bật quyền truy cập vị trí trong cài đặt ứng dụng để sử dụng tính năng này.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToHome();
            },
            child: const Text('Bỏ qua'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              LocationService.openAppSettings();
            },
            child: const Text('Mở cài đặt'),
          ),
        ],
      ),
    );
  }

  void _showLocationServiceDisabledDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Dịch vụ vị trí bị tắt'),
        content: const Text(
          'Vui lòng bật dịch vụ vị trí trong cài đặt thiết bị để sử dụng tính năng này.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToHome();
            },
            child: const Text('Bỏ qua'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              LocationService.openLocationSettings();
            },
            child: const Text('Mở cài đặt'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Lỗi'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToHome();
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
