# 📱 Hướng Dẫn App Icons - Shipper19+

## 🎯 Tổng <PERSON>uan
Đã cập nhật logo ứng dụng từ icon mặc định sang logo.png tùy chỉnh cho tất cả platforms.

## ✅ Đã Hoàn Thành

### 📁 **Files Logo Đã Cập Nhật:**

#### 🤖 **Android Icons:**
```
android/app/src/main/res/
├── mipmap-mdpi/ic_launcher.png     (48x48px)
├── mipmap-hdpi/ic_launcher.png     (72x72px)
├── mipmap-xhdpi/ic_launcher.png    (96x96px)
├── mipmap-xxhdpi/ic_launcher.png   (144x144px)
└── mipmap-xxxhdpi/ic_launcher.png  (192x192px)
```

#### 🌐 **Web Icons:**
```
web/
├── favicon.png                     (32x32px)
└── icons/
    ├── Icon-192.png                (192x192px)
    ├── Icon-512.png                (512x512px)
    ├── Icon-maskable-192.png       (192x192px)
    └── Icon-maskable-512.png       (512x512px)
```

#### 📄 **Configuration Files:**
- `web/manifest.json` - Cập nhật tên app và theme color
- `web/index.html` - Cập nhật title và favicon
- `android/app/src/main/AndroidManifest.xml` - Cập nhật app label

### 🎨 **App Branding Mới:**
- **Tên ứng dụng**: `Shipper19+` (thay vì `shipper19`)
- **Mô tả**: `Shipper19+ - Ứng dụng giao hàng thông minh`
- **Theme color**: `#4CAF50` (xanh lá - phù hợp với theme mới)
- **Logo**: Sử dụng `assets/images/logo.png`

## 🚀 Cách Test Logo Mới

### 1. **Web (Chrome):**
```bash
flutter run -d chrome
```
- Kiểm tra favicon trên tab browser
- Kiểm tra PWA icon khi "Add to Home Screen"

### 2. **Android:**
```bash
flutter build apk --release
flutter install
```
- Kiểm tra icon trong app drawer
- Kiểm tra icon trên home screen

### 3. **iOS (nếu có Mac):**
```bash
flutter build ios --release
```
- Kiểm tra icon trên home screen
- Kiểm tra icon trong App Store

## 🛠️ Scripts Hỗ Trợ

### **Tự Động Tạo Icons:**
```bash
# Python script (yêu cầu Pillow)
python scripts/generate_icons.py

# PowerShell script
powershell -ExecutionPolicy Bypass -File scripts/generate_icons.ps1

# Batch script
scripts/setup_icons.bat
```

### **Thủ Công Tạo Icons:**
1. Truy cập: https://easyappicon.com/
2. Upload `assets/images/logo.png`
3. Download generated icons
4. Replace existing icons trong các thư mục tương ứng

## 📋 Checklist Hoàn Thành

- [x] ✅ Copy logo.png vào tất cả Android mipmap folders
- [x] ✅ Copy logo.png vào web icons folder
- [x] ✅ Cập nhật web/manifest.json
- [x] ✅ Cập nhật web/index.html
- [x] ✅ Cập nhật AndroidManifest.xml
- [x] ✅ Tạo scripts hỗ trợ
- [ ] 🔄 Test trên Android device
- [ ] 🔄 Test trên iOS device (nếu có)
- [ ] 🔄 Test PWA trên web

## 🎯 Kết Quả Mong Đợi

### **Trước:**
- Icon mặc định Flutter (xanh dương)
- Tên app: "shipper19"
- Theme color: #0175C2

### **Sau:**
- Logo tùy chỉnh từ logo.png
- Tên app: "Shipper19+"
- Theme color: #4CAF50 (xanh lá)
- Branding nhất quán trên tất cả platforms

## 🔧 Troubleshooting

### **Logo không hiển thị:**
1. Chạy `flutter clean`
2. Chạy `flutter build [platform]`
3. Kiểm tra file logo.png có tồn tại không
4. Kiểm tra permissions của files

### **Kích thước logo không phù hợp:**
1. Sử dụng tools online để resize
2. Đảm bảo logo có background trong suốt
3. Test trên nhiều kích thước màn hình

### **Android không cập nhật icon:**
1. Uninstall app cũ
2. Rebuild và install lại
3. Clear launcher cache
4. Restart device

## 📚 Tài Liệu Tham Khảo

- [Flutter App Icons Guide](https://docs.flutter.dev/deployment/android#adding-a-launcher-icon)
- [Android Icon Guidelines](https://developer.android.com/guide/practices/ui_guidelines/icon_design_launcher)
- [iOS Icon Guidelines](https://developer.apple.com/design/human-interface-guidelines/app-icons)
- [PWA Icon Guidelines](https://web.dev/add-manifest/)

---

**💡 Lưu ý**: Sau khi cập nhật icons, luôn test trên thiết bị thật để đảm bảo hiển thị đúng!
