import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../providers/orders_provider.dart';
import '../../../data/models/order_model.dart';
import '../../../core/constants/app_constants.dart';

class OrderDetailPage extends ConsumerStatefulWidget {
  final String orderId;

  const OrderDetailPage({super.key, required this.orderId});

  @override
  ConsumerState<OrderDetailPage> createState() => _OrderDetailPageState();
}

class _OrderDetailPageState extends ConsumerState<OrderDetailPage> {
  @override
  void initState() {
    super.initState();
    // Load order detail when page opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(ordersProvider.notifier).loadOrderDetail(widget.orderId);
    });
  }

  @override
  Widget build(BuildContext context) {
    final ordersState = ref.watch(ordersProvider);
    final order = ordersState.selectedOrder;

    return Scaffold(
      appBar: AppBar(
        title: Text('Đơn hàng #${widget.orderId.toString().padLeft(6, '0')}'),
        actions: [
          if (order != null && order.isInProgress)
            PopupMenuButton<String>(
              onSelected: (status) =>
                  _updateOrderStatus(order.id.toString(), status),
              itemBuilder: (context) => [
                if (order.status == AppConstants.orderStatusPendingDriver)
                  const PopupMenuItem(
                    value: AppConstants.orderStatusPicking,
                    child: Text('Đang lấy hàng'),
                  ),
                if (order.status == AppConstants.orderStatusPicking)
                  const PopupMenuItem(
                    value: AppConstants.orderStatusDelivering,
                    child: Text('Đang giao hàng'),
                  ),
                if (order.status == AppConstants.orderStatusDelivering)
                  const PopupMenuItem(
                    value: AppConstants.orderStatusCompleted,
                    child: Text('Hoàn thành'),
                  ),
              ],
            ),
        ],
      ),
      body: ordersState.isLoading
          ? const Center(child: CircularProgressIndicator())
          : ordersState.error != null
          ? _buildErrorState(ordersState.error!)
          : order == null
          ? const Center(child: Text('Không tìm thấy đơn hàng'))
          : _buildOrderDetail(order),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 80, color: Colors.red[400]),
          const SizedBox(height: 16),
          Text(
            'Có lỗi xảy ra',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(color: Colors.red[600]),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: TextStyle(color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              ref.read(ordersProvider.notifier).loadOrderDetail(widget.orderId);
            },
            icon: const Icon(Icons.refresh),
            label: const Text('Thử lại'),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderDetail(OrderModel order) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Status Card
          _buildStatusCard(order),
          const SizedBox(height: 16),

          // Addresses Card
          _buildAddressesCard(order),
          const SizedBox(height: 16),

          // Customer Info Card
          _buildCustomerInfoCard(order),
          const SizedBox(height: 16),

          // Order Summary Card
          _buildOrderSummaryCard(order),
          const SizedBox(height: 16),

          // Action Buttons
          if (order.isInProgress) _buildActionButtons(order),
        ],
      ),
    );
  }

  Widget _buildStatusCard(OrderModel order) {
    Color statusColor;
    String statusText;

    switch (order.status) {
      case AppConstants.orderStatusPendingDriver:
        statusColor = Colors.orange;
        statusText = 'Chờ tài xế';
        break;
      case AppConstants.orderStatusPicking:
        statusColor = Colors.blue;
        statusText = 'Đang lấy hàng';
        break;
      case AppConstants.orderStatusDelivering:
        statusColor = Colors.purple;
        statusText = 'Đang giao hàng';
        break;
      case AppConstants.orderStatusCompleted:
        statusColor = Colors.green;
        statusText = 'Hoàn thành';
        break;
      default:
        statusColor = Colors.grey;
        statusText = order.status;
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: statusColor,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Trạng thái đơn hàng',
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                  Text(
                    statusText,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: statusColor,
                    ),
                  ),
                ],
              ),
            ),
            Text(
              DateFormat('dd/MM/yyyy HH:mm').format(order.createdAt),
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAddressesCard(OrderModel order) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Địa chỉ',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),

            // Pickup Address
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 12,
                  height: 12,
                  margin: const EdgeInsets.only(top: 4),
                  decoration: const BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Lấy hàng',
                        style: TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                      Text(
                        order.pickupAddress.address,
                        style: const TextStyle(fontSize: 14),
                      ),
                      if (order.shop != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          order.shop!.name,
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Delivery Address
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 12,
                  height: 12,
                  margin: const EdgeInsets.only(top: 4),
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Giao hàng',
                        style: TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                      Text(
                        order.dropoffAddress.address,
                        style: const TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerInfoCard(OrderModel order) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Thông tin người nhận',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                const Icon(Icons.person, size: 20, color: Colors.grey),
                const SizedBox(width: 8),
                Text(order.receiverName),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.phone, size: 20, color: Colors.grey),
                const SizedBox(width: 8),
                Text(order.receiverPhone),
              ],
            ),
            if (order.note != null && order.note!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Icon(Icons.note, size: 20, color: Colors.grey),
                  const SizedBox(width: 8),
                  Expanded(child: Text(order.note!)),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildOrderSummaryCard(OrderModel order) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Tóm tắt đơn hàng',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Số lượng món:'),
                Text('${order.items.length} món'),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Tổng tiền:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(
                  '${order.totalAmount.toStringAsFixed(0)}đ',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(OrderModel order) {
    return Column(
      children: [
        if (order.status == AppConstants.orderStatusPendingDriver)
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () => _updateOrderStatus(
                order.id.toString(),
                AppConstants.orderStatusPicking,
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text(
                'Bắt đầu lấy hàng',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ),
          ),
        if (order.status == AppConstants.orderStatusPicking)
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () => _updateOrderStatus(
                order.id.toString(),
                AppConstants.orderStatusDelivering,
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text(
                'Bắt đầu giao hàng',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ),
          ),
        if (order.status == AppConstants.orderStatusDelivering)
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () => _updateOrderStatus(
                order.id.toString(),
                AppConstants.orderStatusCompleted,
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text(
                'Hoàn thành đơn hàng',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ),
          ),
      ],
    );
  }

  Future<void> _updateOrderStatus(String orderId, String status) async {
    final success = await ref
        .read(ordersProvider.notifier)
        .updateOrderStatus(orderId, status);

    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Cập nhật trạng thái thành công'),
          backgroundColor: Colors.green,
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Không thể cập nhật: ${ref.read(ordersProvider).error ?? "Lỗi không xác định"}',
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
