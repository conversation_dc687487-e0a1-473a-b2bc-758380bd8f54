class ApiEndpoints {
  // Base
  static const String baseUrl = 'http://127.0.0.1:8000';
  static const String apiVersion = '/api/v1';
  static const String fullBaseUrl = '$baseUrl$apiVersion';

  // Auth Endpoints
  static const String register = '/register';
  static const String login = '/login';
  static const String logout = '/logout';
  static const String me = '/me';

  // Customer Endpoints
  static const String customerPrefix = '/customers';
  static const String customerShops = '$customerPrefix/shops';
  static const String customerProducts = '$customerPrefix/products';
  static const String customerOrders = '$customerPrefix/orders';

  // Shop Endpoints
  static const String shopPrefix = '/shops';
  static const String shopProfile = '$shopPrefix/profile';
  static const String shopProducts = '$shopPrefix/products';
  static const String shopOrders = '$shopPrefix/orders';
  static const String shopStats = '$shopPrefix/stats';

  // Driver/Shipper Endpoints
  static const String driverPrefix = '/drivers';
  static const String availableOrders = '$driverPrefix/orders/available';
  static const String orderDetail = '$driverPrefix/orders'; // + /{id}
  static const String acceptOrder = '$driverPrefix/orders'; // + /{id}/accept
  static const String updateOrderStatus =
      '$driverPrefix/orders'; // + /{id}/status
  static const String driverStats = '$driverPrefix/stats';

  // Admin Endpoints
  static const String adminPrefix = '/admin';
  static const String adminUsers = '$adminPrefix/users';
  static const String adminShops = '$adminPrefix/shops';
  static const String adminProducts = '$adminPrefix/products';
  static const String adminDrivers = '$adminPrefix/drivers';
  static const String adminOrders = '$adminPrefix/orders';
  static const String adminConfigs = '$adminPrefix/configs';
  static const String adminPosts = '$adminPrefix/posts';
  static const String adminStats = '$adminPrefix/stats';

  // Helper methods to build full URLs

  // Auth URLs
  static String getRegisterUrl() => '$fullBaseUrl$register';
  static String getLoginUrl() => '$fullBaseUrl$login';
  static String getLogoutUrl() => '$fullBaseUrl$logout';
  static String getMeUrl() => '$fullBaseUrl$me';

  // Customer URLs
  static String getCustomerShopsUrl() => '$fullBaseUrl$customerShops';
  static String getCustomerShopDetailUrl(String shopId) =>
      '$fullBaseUrl$customerShops/$shopId';
  static String getCustomerShopProductsUrl(String shopId) =>
      '$fullBaseUrl$customerShops/$shopId/products';
  static String getCustomerProductsUrl() => '$fullBaseUrl$customerProducts';
  static String getCustomerProductDetailUrl(String productId) =>
      '$fullBaseUrl$customerProducts/$productId';
  static String getCustomerOrdersUrl() => '$fullBaseUrl$customerOrders';
  static String getCustomerOrderDetailUrl(String orderId) =>
      '$fullBaseUrl$customerOrders/$orderId';
  static String getCustomerOrderCancelUrl(String orderId) =>
      '$fullBaseUrl$customerOrders/$orderId/cancel';
  static String getCustomerOrderRateUrl(String orderId) =>
      '$fullBaseUrl$customerOrders/$orderId/rate';

  // Shop URLs
  static String getShopProfileUrl() => '$fullBaseUrl$shopProfile';
  static String getShopProductsUrl() => '$fullBaseUrl$shopProducts';
  static String getShopProductDetailUrl(String productId) =>
      '$fullBaseUrl$shopProducts/$productId';
  static String getShopProductToggleUrl(String productId) =>
      '$fullBaseUrl$shopProducts/$productId/toggle-availability';
  static String getShopOrdersUrl() => '$fullBaseUrl$shopOrders';
  static String getShopOrderDetailUrl(String orderId) =>
      '$fullBaseUrl$shopOrders/$orderId';
  static String getShopStatsUrl() => '$fullBaseUrl$shopStats';

  // Driver URLs
  static String getAvailableOrdersUrl() => '$fullBaseUrl$availableOrders';
  static String getOrderDetailUrl(String orderId) =>
      '$fullBaseUrl$orderDetail/$orderId';
  static String getAcceptOrderUrl(String orderId) =>
      '$fullBaseUrl$acceptOrder/$orderId/accept';
  static String getUpdateOrderStatusUrl(String orderId) =>
      '$fullBaseUrl$updateOrderStatus/$orderId/status';
  static String getDriverStatsUrl() => '$fullBaseUrl$driverStats';

  // Admin URLs
  static String getAdminUsersUrl() => '$fullBaseUrl$adminUsers';
  static String getAdminUserDetailUrl(String userId) =>
      '$fullBaseUrl$adminUsers/$userId';
  static String getAdminShopsUrl() => '$fullBaseUrl$adminShops';
  static String getAdminShopDetailUrl(String shopId) =>
      '$fullBaseUrl$adminShops/$shopId';
  static String getAdminShopOrdersUrl(String shopId) =>
      '$fullBaseUrl$adminShops/$shopId/orders';
  static String getAdminProductsUrl() => '$fullBaseUrl$adminProducts';
  static String getAdminProductStatusUrl(String productId) =>
      '$fullBaseUrl$adminProducts/$productId/status';
  static String getAdminDriversUrl() => '$fullBaseUrl$adminDrivers';
  static String getAdminDriverDetailUrl(String driverId) =>
      '$fullBaseUrl$adminDrivers/$driverId';
  static String getAdminDriverOrdersUrl(String driverId) =>
      '$fullBaseUrl$adminDrivers/$driverId/orders';
  static String getAdminDriverLockUrl(String driverId) =>
      '$fullBaseUrl$adminDrivers/$driverId/lock';
  static String getAdminDriverUnlockUrl(String driverId) =>
      '$fullBaseUrl$adminDrivers/$driverId/unlock';
  static String getAdminOrdersUrl() => '$fullBaseUrl$adminOrders';
  static String getAdminOrderDetailUrl(String orderId) =>
      '$fullBaseUrl$adminOrders/$orderId';
  static String getAdminOrderStatusUrl(String orderId) =>
      '$fullBaseUrl$adminOrders/$orderId/status';
  static String getAdminOrderReassignUrl(String orderId) =>
      '$fullBaseUrl$adminOrders/$orderId/reassign';
  static String getAdminConfigsPricingUrl() =>
      '$fullBaseUrl$adminConfigs/pricing';
  static String getAdminConfigsSurchargesUrl() =>
      '$fullBaseUrl$adminConfigs/surcharges';
  static String getAdminPostsUrl() => '$fullBaseUrl$adminPosts';
  static String getAdminPostDetailUrl(String postId) =>
      '$fullBaseUrl$adminPosts/$postId';
  static String getAdminStatsSummaryUrl() => '$fullBaseUrl$adminStats/summary';
  static String getAdminStatsByShopUrl() => '$fullBaseUrl$adminStats/by-shop';
  static String getAdminStatsRevenueUrl() => '$fullBaseUrl$adminStats/revenue';
}
