import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../data/models/restaurant_model.dart';

abstract class RestaurantRepository {
  Future<Either<Failure, List<RestaurantModel>>> getRestaurants();
  Future<Either<Failure, RestaurantModel?>> getRestaurantById(String id);
  Future<Either<Failure, List<RestaurantModel>>> searchRestaurants(String query);
  Future<Either<Failure, List<RestaurantModel>>> getRestaurantsByCategory(String categoryId);
  Future<Either<Failure, List<VoucherModel>>> getVouchers();
  Future<Either<Failure, List<PostModel>>> getPosts();
}
