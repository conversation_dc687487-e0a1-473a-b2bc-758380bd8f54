import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../data/models/order_model.dart';
import '../../data/models/driver_stats_model.dart';
import '../repositories/driver_repository.dart';

class GetAvailableOrdersUseCase {
  final DriverRepository repository;

  GetAvailableOrdersUseCase(this.repository);

  Future<Either<Failure, List<OrderModel>>> call() async {
    return await repository.getAvailableOrders();
  }
}

class GetOrderDetailUseCase {
  final DriverRepository repository;

  GetOrderDetailUseCase(this.repository);

  Future<Either<Failure, OrderModel>> call(String orderId) async {
    return await repository.getOrderDetail(orderId);
  }
}

class AcceptOrderUseCase {
  final DriverRepository repository;

  AcceptOrderUseCase(this.repository);

  Future<Either<Failure, void>> call(String orderId) async {
    return await repository.acceptOrder(orderId);
  }
}

class UpdateOrderStatusUseCase {
  final DriverRepository repository;

  UpdateOrderStatusUseCase(this.repository);

  Future<Either<Failure, void>> call(String orderId, String status) async {
    return await repository.updateOrderStatus(orderId, status);
  }
}

class GetDriverStatsUseCase {
  final DriverRepository repository;

  GetDriverStatsUseCase(this.repository);

  Future<Either<Failure, DriverStatsModel>> call(String? date) async {
    return await repository.getDriverStats(date);
  }
}
