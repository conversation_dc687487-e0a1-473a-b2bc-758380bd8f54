import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/constants/app_colors.dart';
import '../../../data/datasources/api_service_provider.dart';

class OrderTrackingPage extends ConsumerStatefulWidget {
  final String orderId;

  const OrderTrackingPage({super.key, required this.orderId});

  @override
  ConsumerState<OrderTrackingPage> createState() => _OrderTrackingPageState();
}

class _OrderTrackingPageState extends ConsumerState<OrderTrackingPage> {
  int _currentStep = 0;
  Map<String, dynamic>? _orderDetail;

  final List<Map<String, dynamic>> _orderSteps = [
    {
      'title': 'Đơn hàng đã được xác nhận',
      'subtitle': 'Nhà hàng đã nhận đơn hàng',
      'time': '14:30',
      'icon': Icons.check_circle,
      'isCompleted': true,
    },
    {
      'title': '<PERSON>ang chuẩn bị món ăn',
      'subtitle': 'Nhà hàng đang chuẩn bị đơn hàng',
      'time': '14:35',
      'icon': Icons.restaurant,
      'isCompleted': true,
    },
    {
      'title': 'Shipper đang đến nhà hàng',
      'subtitle': 'Shipper đang trên đường đến lấy hàng',
      'time': '14:45',
      'icon': Icons.motorcycle,
      'isCompleted': true,
    },
    {
      'title': 'Đang giao hàng',
      'subtitle': 'Shipper đang giao hàng đến bạn',
      'time': 'Dự kiến 15:15',
      'icon': Icons.local_shipping,
      'isCompleted': false,
    },
    {
      'title': 'Đã giao hàng',
      'subtitle': 'Đơn hàng đã được giao thành công',
      'time': '',
      'icon': Icons.done_all,
      'isCompleted': false,
    },
  ];

  @override
  void initState() {
    super.initState();
    _fetchOrder();
  }

  Future<void> _fetchOrder() async {
    try {
      final res = await ref
          .read(customerApiServiceProvider)
          .getOrderDetail(widget.orderId);
      if (res.success) {
        setState(() {
          _orderDetail = res.data;
          // Optional: map status -> step index
          final status = (res.data['status'] ?? '').toString();
          _currentStep = _mapStatusToStep(status);
        });
      }
    } catch (_) {}
  }

  int _mapStatusToStep(String status) {
    switch (status) {
      case 'NEW':
        return 0;
      case 'PICKING':
      case 'PENDING_DRIVER':
        return 2;
      case 'DELIVERING':
        return 3;
      case 'COMPLETED':
        return 4;
      default:
        return 1; // preparing
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Đơn hàng #${widget.orderId}'),
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _showOrderDetails,
            icon: const Icon(Icons.info_outline),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Map Section (Placeholder)
            _buildMapSection(),

            // Shipper Info Section
            _buildShipperInfoSection(),

            // Order Timeline Section
            _buildOrderTimelineSection(),

            // Order Items Section
            _buildOrderItemsSection(),
          ],
        ),
      ),
      bottomNavigationBar: _buildActionButtons(),
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Polling mỗi 10s để cập nhật trạng thái
    Future.doWhile(() async {
      await Future.delayed(const Duration(seconds: 10));
      if (!mounted) return false;
      await _fetchOrder();
      // Dừng khi hoàn thành/cancel
      final status = (_orderDetail?['status'] ?? '').toString();
      return ![
        'COMPLETED',
        'CANCELLED',
        'CANCELLED_BY_SHOP',
        'CANCELLED_BY_CUSTOMER',
      ].contains(status);
    });
  }

  Widget _buildMapSection() {
    return Container(
      height: 200,
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Stack(
        children: [
          // Placeholder for map
          Container(
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.map, size: 48, color: Colors.grey),
                  SizedBox(height: 8),
                  Text(
                    'Bản đồ theo dõi shipper',
                    style: TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            ),
          ),

          // ETA Card
          Positioned(
            top: 16,
            left: 16,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.access_time, size: 16, color: AppColors.primary),
                  const SizedBox(width: 4),
                  const Text(
                    'Dự kiến: 15:15',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShipperInfoSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 25,
            backgroundColor: AppColors.primary,
            child: const Text(
              'NV',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 16),
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Nguyễn Văn A',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                Text(
                  'Shipper • 4.8 ⭐ (234 đánh giá)',
                  style: TextStyle(color: Colors.grey, fontSize: 12),
                ),
                Text(
                  'Biển số: 59H1-12345',
                  style: TextStyle(color: Colors.grey, fontSize: 12),
                ),
              ],
            ),
          ),
          Row(
            children: [
              IconButton(
                onPressed: _callShipper,
                icon: const Icon(Icons.phone),
                style: IconButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                onPressed: _chatWithShipper,
                icon: const Icon(Icons.chat),
                style: IconButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOrderTimelineSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Trạng thái đơn hàng',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          ..._orderSteps.asMap().entries.map((entry) {
            final index = entry.key;
            final step = entry.value;
            final isActive = index == _currentStep;
            final isCompleted = step['isCompleted'] as bool;

            return _buildTimelineStep(
              step['title'],
              step['subtitle'],
              step['time'],
              step['icon'],
              isCompleted: isCompleted,
              isActive: isActive,
              isLast: index == _orderSteps.length - 1,
            );
          }),
        ],
      ),
    );
  }

  Widget _buildTimelineStep(
    String title,
    String subtitle,
    String time,
    IconData icon, {
    required bool isCompleted,
    required bool isActive,
    required bool isLast,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: isCompleted
                    ? Colors.green
                    : isActive
                    ? AppColors.primary
                    : Colors.grey[300],
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: isCompleted || isActive
                    ? Colors.white
                    : Colors.grey[600],
                size: 20,
              ),
            ),
            if (!isLast)
              Container(
                width: 2,
                height: 40,
                color: isCompleted ? Colors.green : Colors.grey[300],
              ),
          ],
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(bottom: 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        title,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: isCompleted || isActive
                              ? Colors.black
                              : Colors.grey[600],
                        ),
                      ),
                    ),
                    if (time.isNotEmpty)
                      Text(
                        time,
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildOrderItemsSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Chi tiết đơn hàng',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          // Sample order items
          _buildOrderItem('Phở Bò Tái', 2, 85000),
          _buildOrderItem('Bánh Mì Thịt Nướng', 1, 25000),
          const Divider(),
          _buildTotalRow('Tạm tính', 195000),
          _buildTotalRow('Phí giao hàng', 15000),
          _buildTotalRow('Phí dịch vụ', 3000),
          const Divider(),
          _buildTotalRow('Tổng cộng', 213000, isTotal: true),
        ],
      ),
    );
  }

  Widget _buildOrderItem(String name, int quantity, double price) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Center(
              child: Text(
                '$quantity',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              name,
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          Text(
            '${price.toStringAsFixed(0)}đ',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildTotalRow(String label, double amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            '${amount.toStringAsFixed(0)}đ',
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? AppColors.primary : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: _cancelOrder,
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
                side: const BorderSide(color: Colors.red),
              ),
              child: const Text('Hủy đơn', style: TextStyle(color: Colors.red)),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _rateOrder,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: const Text(
                'Đánh giá',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showOrderDetails() {
    // Show order details modal
  }

  void _callShipper() {
    // Implement call functionality
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Đang gọi cho shipper...')));
  }

  void _chatWithShipper() {
    // Implement chat functionality
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Mở chat với shipper...')));
  }

  void _cancelOrder() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Hủy đơn hàng'),
        content: const Text('Bạn có chắc chắn muốn hủy đơn hàng này?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Không'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Implement cancel order logic
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Đơn hàng đã được hủy')),
              );
            },
            child: const Text('Hủy đơn', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _rateOrder() {
    // Navigate to rating page
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Mở trang đánh giá...')));
  }
}
