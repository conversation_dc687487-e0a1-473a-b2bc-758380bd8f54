import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'driver_model.g.dart';

@JsonSerializable()
class DriverModel extends Equatable {
  final int id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_id')
  final int userId;
  final String name;
  final double rating;
  @Json<PERSON>ey(name: 'active_order_count')
  final int activeOrderCount;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_online')
  final bool isOnline;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_locked')
  final bool isLocked;
  @<PERSON>son<PERSON><PERSON>(name: 'vehicle_type')
  final String vehicleType;
  @Json<PERSON>ey(name: 'created_at')
  final DateTime createdAt;
  @Json<PERSON>ey(name: 'updated_at')
  final DateTime updatedAt;

  const DriverModel({
    required this.id,
    required this.userId,
    required this.name,
    required this.rating,
    required this.activeOrderCount,
    required this.isOnline,
    required this.isLocked,
    required this.vehicleType,
    required this.createdAt,
    required this.updatedAt,
  });

  factory DriverModel.fromJson(Map<String, dynamic> json) => _$DriverModelFromJson(json);
  Map<String, dynamic> toJson() => _$DriverModelToJson(this);

  bool canAcceptOrder() {
    return isOnline && !isLocked && activeOrderCount < 3;
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        name,
        rating,
        activeOrderCount,
        isOnline,
        isLocked,
        vehicleType,
        createdAt,
        updatedAt,
      ];
}
