import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'driver_stats_model.g.dart';

@JsonSerializable()
class DriverStatsModel extends Equatable {
  @Json<PERSON>ey(name: 'total_orders')
  final int totalOrders;
  @Json<PERSON>ey(name: 'completed_orders')
  final int completedOrders;
  @Json<PERSON>ey(name: 'total_earnings', fromJson: _doubleFromString)
  final double totalEarnings;

  const DriverStatsModel({
    required this.totalOrders,
    required this.completedOrders,
    required this.totalEarnings,
  });

  factory DriverStatsModel.fromJson(Map<String, dynamic> json) =>
      _$DriverStatsModelFromJson(json);
  Map<String, dynamic> toJson() => _$DriverStatsModelToJson(this);

  double get completionRate {
    if (totalOrders == 0) return 0.0;
    return (completedOrders / totalOrders) * 100;
  }

  @override
  List<Object?> get props => [totalOrders, completedOrders, totalEarnings];
}

// Helper function for JSON conversion
double _doubleFromString(dynamic value) {
  if (value == null) return 0.0;
  if (value is double) return value;
  if (value is int) return value.toDouble();
  if (value is String) return double.tryParse(value) ?? 0.0;
  return 0.0;
}
