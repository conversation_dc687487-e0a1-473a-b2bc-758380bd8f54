import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../providers/auth_provider.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/services/otp_service.dart';
import '../../widgets/app_logo.dart';
import '../../../core/constants/app_constants.dart';

class OtpVerificationPage extends ConsumerStatefulWidget {
  final String identifier; // Can be phone number or email
  final String type; // 'phone' or 'email'

  const OtpVerificationPage({
    super.key,
    required this.identifier,
    this.type = 'phone',
  });

  @override
  ConsumerState<OtpVerificationPage> createState() =>
      _OtpVerificationPageState();
}

class _OtpVerificationPageState extends ConsumerState<OtpVerificationPage> {
  final _formKey = GlobalKey<FormState>();
  final List<TextEditingController> _otpControllers = List.generate(
    6,
    (index) => TextEditingController(),
  );
  final List<FocusNode> _focusNodes = List.generate(6, (index) => FocusNode());
  bool _isLoading = false;
  int _countdown = 60;

  @override
  void initState() {
    super.initState();
    _startCountdown();
  }

  @override
  void dispose() {
    for (var controller in _otpControllers) {
      controller.dispose();
    }
    for (var node in _focusNodes) {
      node.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);

    // Listen to auth state changes
    ref.listen<AuthState>(authProvider, (previous, next) {
      if (next.isAuthenticated && next.user != null) {
        final role = next.user!.role;
        String route;
        switch (role) {
          case AppConstants.roleShop:
            route = '/shop';
            break;
          case AppConstants.roleShipper:
            route = '/shipper';
            break;
          case AppConstants.roleCustomer:
          default:
            route = '/customer';
            break;
        }
        context.go(route);
      }
    });

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => context.pop(),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: 40),

                // Logo
                const Center(child: AppLogo.large()),
                const SizedBox(height: 40),

                // Title
                Text(
                  'Xác thực OTP',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),

                Text(
                  widget.type == 'email'
                      ? 'Mã OTP đã được gửi đến email\n${OtpService.getMaskedIdentifier(widget.identifier)}'
                      : 'Mã OTP đã được gửi đến số\n${OtpService.getMaskedIdentifier(widget.identifier)}',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 40),

                // OTP Input Fields
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: List.generate(6, (index) {
                    return SizedBox(
                      width: 45,
                      height: 55,
                      child: TextFormField(
                        controller: _otpControllers[index],
                        focusNode: _focusNodes[index],
                        keyboardType: TextInputType.number,
                        textAlign: TextAlign.center,
                        maxLength: 1,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                        decoration: InputDecoration(
                          counterText: '',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(
                              color: AppColors.primary,
                              width: 2,
                            ),
                          ),
                        ),
                        onChanged: (value) {
                          if (value.isNotEmpty && index < 5) {
                            _focusNodes[index + 1].requestFocus();
                          } else if (value.isEmpty && index > 0) {
                            _focusNodes[index - 1].requestFocus();
                          }

                          // Auto verify when all fields are filled
                          if (index == 5 && value.isNotEmpty) {
                            _verifyOtp();
                          }
                        },
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return '';
                          }
                          return null;
                        },
                      ),
                    );
                  }),
                ),
                const SizedBox(height: 32),

                // Countdown and Resend
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (_countdown > 0) ...[
                      Text(
                        'Gửi lại mã sau $_countdown giây',
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                    ] else ...[
                      TextButton(
                        onPressed: _resendOtp,
                        child: const Text('Gửi lại mã OTP'),
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 32),

                // Verify Button
                ElevatedButton(
                  onPressed: _isLoading ? null : _verifyOtp,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text('Xác thực', style: TextStyle(fontSize: 16)),
                ),

                // Error Message
                if (authState.error != null) ...[
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.red[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.red[200]!),
                    ),
                    child: Text(
                      authState.error!,
                      style: TextStyle(color: Colors.red[700]),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
                const SizedBox(height: 32),

                // Change phone number
                TextButton(
                  onPressed: () => context.pop(),
                  child: const Text('Thay đổi số điện thoại'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _verifyOtp() async {
    // Get OTP from all controllers
    String otp = _otpControllers.map((controller) => controller.text).join();

    if (otp.length != 6) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Vui lòng nhập đầy đủ mã OTP'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Verify OTP using OtpService
      final isValid = OtpService.verifyOtp(widget.identifier, otp);

      if (!mounted) return;

      if (isValid) {
        // Simulate successful login
        await ref
            .read(authProvider.notifier)
            .login(
              '<EMAIL>', // Demo email - in real app, use identifier
              'password', // Demo password
            );

        // Navigation will be handled by auth state change
      } else {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Mã OTP không chính xác hoặc đã hết hạn'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Lỗi xác thực OTP: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _resendOtp() async {
    setState(() {
      _countdown = 60;
    });
    _startCountdown();

    // Clear OTP fields
    for (var controller in _otpControllers) {
      controller.clear();
    }
    _focusNodes[0].requestFocus();

    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Mã OTP đã được gửi lại đến ${OtpService.getMaskedIdentifier(widget.identifier)}',
        ),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _startCountdown() {
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted && _countdown > 0) {
        setState(() {
          _countdown--;
        });
        _startCountdown();
      }
    });
  }
}
