import 'dart:math';
import 'package:dio/dio.dart';

class OtpService {
  static final Dio _dio = Dio();

  // In-memory storage for demo purposes
  // In production, this should be handled by backend
  static final Map<String, String> _otpStorage = {};
  static final Map<String, DateTime> _otpExpiry = {};

  /// Generate a 6-digit OTP
  static String _generateOtp() {
    final random = Random();
    return (100000 + random.nextInt(900000)).toString();
  }

  /// Send OTP to phone number
  static Future<bool> sendOtpToPhone(String phoneNumber) async {
    try {
      // Generate OTP
      final otp = _generateOtp();

      // Store OTP with expiry (5 minutes)
      _otpStorage[phoneNumber] = otp;
      _otpExpiry[phoneNumber] = DateTime.now().add(const Duration(minutes: 5));

      // In production, integrate with SMS service like Twilio, Firebase, etc.
      // For demo, we'll just print the OTP
      print('OTP for $phoneNumber: $otp');

      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 1));

      // For demo purposes, always return success
      // In production, handle actual SMS sending errors
      return true;
    } catch (e) {
      print('Error sending OTP: $e');
      return false;
    }
  }

  /// Verify OTP
  static bool verifyOtp(String identifier, String otp) {
    try {
      // Check if OTP exists
      if (!_otpStorage.containsKey(identifier)) {
        return false;
      }

      // Check if OTP is expired
      final expiry = _otpExpiry[identifier];
      if (expiry == null || DateTime.now().isAfter(expiry)) {
        // Clean up expired OTP
        _otpStorage.remove(identifier);
        _otpExpiry.remove(identifier);
        return false;
      }

      // Verify OTP
      final storedOtp = _otpStorage[identifier];
      final isValid = storedOtp == otp;

      // Clean up used OTP
      if (isValid) {
        _otpStorage.remove(identifier);
        _otpExpiry.remove(identifier);
      }

      return isValid;
    } catch (e) {
      print('Error verifying OTP: $e');
      return false;
    }
  }

  /// Check if OTP is still valid (not expired)
  static bool isOtpValid(String identifier) {
    if (!_otpStorage.containsKey(identifier)) {
      return false;
    }

    final expiry = _otpExpiry[identifier];
    if (expiry == null || DateTime.now().isAfter(expiry)) {
      // Clean up expired OTP
      _otpStorage.remove(identifier);
      _otpExpiry.remove(identifier);
      return false;
    }

    return true;
  }

  /// Get remaining time for OTP in seconds
  static int getRemainingTime(String identifier) {
    final expiry = _otpExpiry[identifier];
    if (expiry == null) return 0;

    final remaining = expiry.difference(DateTime.now()).inSeconds;
    return remaining > 0 ? remaining : 0;
  }

  /// Clear all stored OTPs (for testing/cleanup)
  static void clearAllOtps() {
    _otpStorage.clear();
    _otpExpiry.clear();
  }

  /// Validate phone number format
  static bool isValidPhoneNumber(String phone) {
    // Vietnamese phone number pattern
    final phoneRegex = RegExp(r'^(0|\+84)[3|5|7|8|9][0-9]{8}$');
    return phoneRegex.hasMatch(phone);
  }

  /// Validate email format
  static bool isValidEmail(String email) {
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    return emailRegex.hasMatch(email);
  }

  /// Format phone number for display
  static String formatPhoneNumber(String phone) {
    if (phone.startsWith('+84')) {
      return phone.replaceFirst('+84', '0');
    }
    return phone;
  }

  /// Get masked identifier for display (e.g., 098****567 or test***@gmail.com)
  static String getMaskedIdentifier(String identifier) {
    if (isValidPhoneNumber(identifier)) {
      if (identifier.length >= 10) {
        return '${identifier.substring(0, 3)}****${identifier.substring(identifier.length - 3)}';
      }
    } else if (isValidEmail(identifier)) {
      final parts = identifier.split('@');
      if (parts.length == 2) {
        final username = parts[0];
        final domain = parts[1];
        if (username.length > 3) {
          return '${username.substring(0, 3)}***@$domain';
        }
      }
    }
    return identifier;
  }
}
