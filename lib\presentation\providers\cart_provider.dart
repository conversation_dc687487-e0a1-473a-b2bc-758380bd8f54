import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../../data/models/product_model.dart';

class CartItem {
  final String id;
  final ProductModel product;
  final int quantity;
  final ProductVariantModel? selectedVariant;
  final List<ProductToppingModel> selectedToppings;
  final String? note;
  final double totalPrice;

  const CartItem({
    required this.id,
    required this.product,
    required this.quantity,
    this.selectedVariant,
    this.selectedToppings = const [],
    this.note,
    required this.totalPrice,
  });

  CartItem copyWith({
    String? id,
    ProductModel? product,
    int? quantity,
    ProductVariantModel? selectedVariant,
    List<ProductToppingModel>? selectedToppings,
    String? note,
    double? totalPrice,
  }) {
    return CartItem(
      id: id ?? this.id,
      product: product ?? this.product,
      quantity: quantity ?? this.quantity,
      selectedVariant: selectedVariant ?? this.selectedVariant,
      selectedToppings: selectedToppings ?? this.selectedToppings,
      note: note ?? this.note,
      totalPrice: totalPrice ?? this.totalPrice,
    );
  }

  double calculatePrice() {
    double basePrice = product.price;

    // Add variant price
    if (selectedVariant != null) {
      basePrice += selectedVariant!.additionalPrice;
    }

    // Add toppings price
    for (final topping in selectedToppings) {
      basePrice += topping.price;
    }

    return basePrice * quantity;
  }
}

class CartState {
  final List<CartItem> items;
  final String? restaurantId;
  final String? restaurantName;
  final double deliveryFee;
  final double serviceFee;
  final double discount;

  const CartState({
    this.items = const [],
    this.restaurantId,
    this.restaurantName,
    this.deliveryFee = 0,
    this.serviceFee = 0,
    this.discount = 0,
  });

  CartState copyWith({
    List<CartItem>? items,
    String? restaurantId,
    String? restaurantName,
    double? deliveryFee,
    double? serviceFee,
    double? discount,
  }) {
    return CartState(
      items: items ?? this.items,
      restaurantId: restaurantId ?? this.restaurantId,
      restaurantName: restaurantName ?? this.restaurantName,
      deliveryFee: deliveryFee ?? this.deliveryFee,
      serviceFee: serviceFee ?? this.serviceFee,
      discount: discount ?? this.discount,
    );
  }

  double get subtotal {
    return items.fold(0, (sum, item) => sum + item.totalPrice);
  }

  double get total {
    return subtotal + deliveryFee + serviceFee - discount;
  }

  int get itemCount {
    return items.fold(0, (sum, item) => sum + item.quantity);
  }

  bool get isEmpty => items.isEmpty;
  bool get isNotEmpty => items.isNotEmpty;
}

class CartNotifier extends StateNotifier<CartState> {
  CartNotifier() : super(const CartState());

  void addItem({
    required ProductModel product,
    required String restaurantId,
    required String restaurantName,
    int quantity = 1,
    ProductVariantModel? selectedVariant,
    List<ProductToppingModel> selectedToppings = const [],
    String? note,
  }) {
    // Check if cart is from different restaurant
    if (state.restaurantId != null &&
        state.restaurantId!.isNotEmpty &&
        restaurantId.isNotEmpty &&
        state.restaurantId != restaurantId) {
      // Different restaurant: in real app confirm; here we clear to proceed
      state = const CartState();
    }

    final cartItem = CartItem(
      id: const Uuid().v4(),
      product: product,
      quantity: quantity,
      selectedVariant: selectedVariant,
      selectedToppings: selectedToppings,
      note: note,
      totalPrice: _calculateItemPrice(
        product,
        quantity,
        selectedVariant,
        selectedToppings,
      ),
    );

    // Check if same item already exists
    final existingIndex = state.items.indexWhere(
      (item) =>
          item.product.id == product.id &&
          item.selectedVariant?.id == selectedVariant?.id &&
          _compareToppings(item.selectedToppings, selectedToppings) &&
          item.note == note,
    );

    List<CartItem> updatedItems;
    if (existingIndex != -1) {
      // Update existing item quantity
      final existingItem = state.items[existingIndex];
      final updatedItem = existingItem.copyWith(
        quantity: existingItem.quantity + quantity,
        totalPrice: _calculateItemPrice(
          product,
          existingItem.quantity + quantity,
          selectedVariant,
          selectedToppings,
        ),
      );
      updatedItems = [...state.items];
      updatedItems[existingIndex] = updatedItem;
    } else {
      // Add new item
      updatedItems = [...state.items, cartItem];
    }

    state = state.copyWith(
      items: updatedItems,
      restaurantId: restaurantId,
      restaurantName: restaurantName,
      deliveryFee: 15000, // Default delivery fee
      serviceFee: 3000, // Default service fee
    );
  }

  void updateItemQuantity(String itemId, int quantity) {
    if (quantity <= 0) {
      removeItem(itemId);
      return;
    }

    final updatedItems = state.items.map((item) {
      if (item.id == itemId) {
        return item.copyWith(
          quantity: quantity,
          totalPrice: _calculateItemPrice(
            item.product,
            quantity,
            item.selectedVariant,
            item.selectedToppings,
          ),
        );
      }
      return item;
    }).toList();

    state = state.copyWith(items: updatedItems);
  }

  void removeItem(String itemId) {
    final updatedItems = state.items
        .where((item) => item.id != itemId)
        .toList();

    if (updatedItems.isEmpty) {
      state = const CartState();
    } else {
      state = state.copyWith(items: updatedItems);
    }
  }

  void clearCart() {
    state = const CartState();
  }

  void applyDiscount(double discountAmount) {
    state = state.copyWith(discount: discountAmount);
  }

  double _calculateItemPrice(
    ProductModel product,
    int quantity,
    ProductVariantModel? selectedVariant,
    List<ProductToppingModel> selectedToppings,
  ) {
    double basePrice = product.price;

    if (selectedVariant != null) {
      basePrice += selectedVariant.additionalPrice;
    }

    for (final topping in selectedToppings) {
      basePrice += topping.price;
    }

    return basePrice * quantity;
  }

  bool _compareToppings(
    List<ProductToppingModel> list1,
    List<ProductToppingModel> list2,
  ) {
    if (list1.length != list2.length) return false;

    final ids1 = list1.map((t) => t.id).toSet();
    final ids2 = list2.map((t) => t.id).toSet();

    return ids1.containsAll(ids2) && ids2.containsAll(ids1);
  }
}

final cartProvider = StateNotifierProvider<CartNotifier, CartState>((ref) {
  return CartNotifier();
});
