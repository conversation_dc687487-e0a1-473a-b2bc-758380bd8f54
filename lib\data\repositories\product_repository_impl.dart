import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../core/errors/exceptions.dart';
import '../../domain/repositories/product_repository.dart';
import '../datasources/local_data_source.dart';
import '../models/product_model.dart';
import '../models/shop_model.dart';

class ProductRepositoryImpl implements ProductRepository {
  final LocalDataSource localDataSource;

  ProductRepositoryImpl({required this.localDataSource});

  @override
  Future<Either<Failure, List<ProductModel>>> getProducts() async {
    try {
      final products = await localDataSource.getProducts();
      return Right(products);
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(CacheFailure('Lỗi tải danh sách sản phẩm: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, ProductModel?>> getProductById(String id) async {
    try {
      final products = await localDataSource.getProducts();
      final product = products
          .where((product) => product.id.toString() == id)
          .firstOrNull;
      return Right(product);
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(CacheFailure('Lỗi tải thông tin sản phẩm: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<ProductModel>>> getProductsByShop(
    String shopId,
  ) async {
    try {
      final products = await localDataSource.getProducts();
      final filteredProducts = products
          .where((product) => product.shopId?.toString() == shopId)
          .toList();
      return Right(filteredProducts);
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(
        CacheFailure('Lỗi tải sản phẩm theo cửa hàng: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Either<Failure, List<ProductModel>>> getProductsByCategory(
    String categoryId,
  ) async {
    try {
      final products = await localDataSource.getProducts();
      final filteredProducts = products
          .where((product) => product.categoryId?.toString() == categoryId)
          .toList();
      return Right(filteredProducts);
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(
        CacheFailure('Lỗi tải sản phẩm theo danh mục: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Either<Failure, List<ProductModel>>> searchProducts(
    String query,
  ) async {
    try {
      final products = await localDataSource.getProducts();
      final filteredProducts = products
          .where(
            (product) =>
                product.name.toLowerCase().contains(query.toLowerCase()) ||
                (product.description?.toLowerCase().contains(
                      query.toLowerCase(),
                    ) ??
                    false),
          )
          .toList();
      return Right(filteredProducts);
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(CacheFailure('Lỗi tìm kiếm sản phẩm: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<CategoryModel>>> getCategories() async {
    try {
      final categories = await localDataSource.getCategories();
      return Right(categories);
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(CacheFailure('Lỗi tải danh mục: ${e.toString()}'));
    }
  }
}
