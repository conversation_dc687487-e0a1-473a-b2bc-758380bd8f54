import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/constants/app_colors.dart';

class AddressManagementPage extends ConsumerStatefulWidget {
  const AddressManagementPage({super.key});

  @override
  ConsumerState<AddressManagementPage> createState() => _AddressManagementPageState();
}

class _AddressManagementPageState extends ConsumerState<AddressManagementPage> {
  // Sample addresses (in real app, this would come from API)
  List<Map<String, dynamic>> _addresses = [
    {
      'id': '1',
      'label': 'Nhà',
      'name': '<PERSON><PERSON><PERSON><PERSON>n <PERSON>',
      'phone': '0123456789',
      'address': '123 <PERSON><PERSON><PERSON><PERSON>, Phường 4, Quận 5, TP.HCM',
      'isDefault': true,
    },
    {
      'id': '2',
      'label': 'Công ty',
      'name': '<PERSON><PERSON><PERSON><PERSON>',
      'phone': '0987654321',
      'address': '456 <PERSON><PERSON>, Phường 12, Quận 3, TP.HCM',
      'isDefault': false,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Địa chỉ giao hàng'),
        elevation: 0,
      ),
      body: _addresses.isEmpty
          ? _buildEmptyState()
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _addresses.length,
              itemBuilder: (context, index) {
                final address = _addresses[index];
                return _buildAddressCard(address, index);
              },
            ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showAddAddressDialog,
        backgroundColor: AppColors.primary,
        icon: const Icon(Icons.add, color: Colors.white),
        label: const Text(
          'Thêm địa chỉ',
          style: TextStyle(color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.location_off,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Chưa có địa chỉ nào',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Thêm địa chỉ giao hàng để đặt món dễ dàng hơn',
            style: TextStyle(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: _showAddAddressDialog,
            icon: const Icon(Icons.add),
            label: const Text('Thêm địa chỉ đầu tiên'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddressCard(Map<String, dynamic> address, int index) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: address['isDefault'] ? AppColors.primary : Colors.grey[300],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    address['label'],
                    style: TextStyle(
                      color: address['isDefault'] ? Colors.white : Colors.grey[700],
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (address['isDefault']) ...[
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Text(
                      'Mặc định',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
                const Spacer(),
                PopupMenuButton<String>(
                  onSelected: (value) => _handleAddressAction(value, address, index),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, size: 18),
                          SizedBox(width: 8),
                          Text('Chỉnh sửa'),
                        ],
                      ),
                    ),
                    if (!address['isDefault'])
                      const PopupMenuItem(
                        value: 'setDefault',
                        child: Row(
                          children: [
                            Icon(Icons.star, size: 18),
                            SizedBox(width: 8),
                            Text('Đặt làm mặc định'),
                          ],
                        ),
                      ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, size: 18, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Xóa', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(Icons.person, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 8),
                Text(
                  address['name'],
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(width: 16),
                Icon(Icons.phone, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 8),
                Text(
                  address['phone'],
                  style: TextStyle(
                    color: Colors.grey[700],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    address['address'],
                    style: TextStyle(
                      color: Colors.grey[700],
                      fontSize: 14,
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _handleAddressAction(String action, Map<String, dynamic> address, int index) {
    switch (action) {
      case 'edit':
        _showEditAddressDialog(address, index);
        break;
      case 'setDefault':
        _setDefaultAddress(index);
        break;
      case 'delete':
        _showDeleteConfirmation(address, index);
        break;
    }
  }

  void _setDefaultAddress(int index) {
    setState(() {
      // Remove default from all addresses
      for (var addr in _addresses) {
        addr['isDefault'] = false;
      }
      // Set new default
      _addresses[index]['isDefault'] = true;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Đã đặt làm địa chỉ mặc định'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showDeleteConfirmation(Map<String, dynamic> address, int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Xóa địa chỉ'),
        content: Text('Bạn có chắc chắn muốn xóa địa chỉ "${address['label']}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteAddress(index);
            },
            child: const Text('Xóa', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _deleteAddress(int index) {
    final wasDefault = _addresses[index]['isDefault'];
    
    setState(() {
      _addresses.removeAt(index);
      
      // If deleted address was default and there are other addresses, set first one as default
      if (wasDefault && _addresses.isNotEmpty) {
        _addresses[0]['isDefault'] = true;
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Đã xóa địa chỉ'),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showAddAddressDialog() {
    _showAddressDialog(null, -1);
  }

  void _showEditAddressDialog(Map<String, dynamic> address, int index) {
    _showAddressDialog(address, index);
  }

  void _showAddressDialog(Map<String, dynamic>? address, int index) {
    final isEditing = address != null;
    final labelController = TextEditingController(text: address?['label'] ?? '');
    final nameController = TextEditingController(text: address?['name'] ?? '');
    final phoneController = TextEditingController(text: address?['phone'] ?? '');
    final addressController = TextEditingController(text: address?['address'] ?? '');
    bool isDefault = address?['isDefault'] ?? _addresses.isEmpty;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text(isEditing ? 'Chỉnh sửa địa chỉ' : 'Thêm địa chỉ mới'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: labelController,
                  decoration: const InputDecoration(
                    labelText: 'Nhãn địa chỉ (Nhà, Công ty, ...)',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'Họ và tên',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: phoneController,
                  decoration: const InputDecoration(
                    labelText: 'Số điện thoại',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.phone,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: addressController,
                  decoration: const InputDecoration(
                    labelText: 'Địa chỉ chi tiết',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
                const SizedBox(height: 16),
                CheckboxListTile(
                  title: const Text('Đặt làm địa chỉ mặc định'),
                  value: isDefault,
                  onChanged: (value) {
                    setDialogState(() {
                      isDefault = value ?? false;
                    });
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Hủy'),
            ),
            ElevatedButton(
              onPressed: () {
                if (_validateAddressForm(labelController, nameController, phoneController, addressController)) {
                  _saveAddress(
                    isEditing,
                    index,
                    labelController.text,
                    nameController.text,
                    phoneController.text,
                    addressController.text,
                    isDefault,
                  );
                  Navigator.of(context).pop();
                }
              },
              child: Text(isEditing ? 'Cập nhật' : 'Thêm'),
            ),
          ],
        ),
      ),
    );
  }

  bool _validateAddressForm(
    TextEditingController label,
    TextEditingController name,
    TextEditingController phone,
    TextEditingController address,
  ) {
    if (label.text.trim().isEmpty ||
        name.text.trim().isEmpty ||
        phone.text.trim().isEmpty ||
        address.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Vui lòng điền đầy đủ thông tin'),
          backgroundColor: Colors.red,
        ),
      );
      return false;
    }
    return true;
  }

  void _saveAddress(
    bool isEditing,
    int index,
    String label,
    String name,
    String phone,
    String address,
    bool isDefault,
  ) {
    final newAddress = {
      'id': isEditing ? _addresses[index]['id'] : DateTime.now().millisecondsSinceEpoch.toString(),
      'label': label,
      'name': name,
      'phone': phone,
      'address': address,
      'isDefault': isDefault,
    };

    setState(() {
      if (isDefault) {
        // Remove default from all addresses
        for (var addr in _addresses) {
          addr['isDefault'] = false;
        }
      }

      if (isEditing) {
        _addresses[index] = newAddress;
      } else {
        _addresses.add(newAddress);
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(isEditing ? 'Đã cập nhật địa chỉ' : 'Đã thêm địa chỉ mới'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
