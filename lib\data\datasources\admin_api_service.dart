import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import '../models/api_response_model.dart';

part 'admin_api_service.g.dart';

@RestApi()
abstract class AdminApiService {
  factory AdminApiService(Dio dio, {String baseUrl}) = _AdminApiService;

  // Users Management
  @GET('/admin/users')
  Future<PaginatedResponseModel> getUsers(
    @Query('page') int? page,
    @Query('per_page') int? perPage,
    @Query('search') String? search,
    @Query('role') String? role,
  );

  @POST('/admin/users')
  Future<BaseApiResponseModel> createUser(
    @Body() Map<String, dynamic> userData,
  );

  @GET('/admin/users/{id}')
  Future<BaseApiResponseModel> getUserDetail(@Path('id') String userId);

  @PUT('/admin/users/{id}')
  Future<BaseApiResponseModel> updateUser(
    @Path('id') String userId,
    @Body() Map<String, dynamic> userData,
  );

  @DELETE('/admin/users/{id}')
  Future<SimpleApiResponseModel> deleteUser(@Path('id') String userId);

  // Shops Management
  @GET('/admin/shops')
  Future<PaginatedResponseModel> getShops(
    @Query('page') int? page,
    @Query('per_page') int? perPage,
    @Query('search') String? search,
  );

  @POST('/admin/shops')
  Future<BaseApiResponseModel> createShop(
    @Body() Map<String, dynamic> shopData,
  );

  @GET('/admin/shops/{id}')
  Future<BaseApiResponseModel> getShopDetail(@Path('id') String shopId);

  @PUT('/admin/shops/{id}')
  Future<BaseApiResponseModel> updateShop(
    @Path('id') String shopId,
    @Body() Map<String, dynamic> shopData,
  );

  @DELETE('/admin/shops/{id}')
  Future<SimpleApiResponseModel> deleteShop(@Path('id') String shopId);

  @GET('/admin/shops/{id}/orders')
  Future<PaginatedResponseModel> getShopOrders(
    @Path('id') String shopId,
    @Query('page') int? page,
    @Query('per_page') int? perPage,
    @Query('status') String? status,
  );

  // Products Management
  @GET('/admin/products')
  Future<PaginatedResponseModel> getProducts(
    @Query('page') int? page,
    @Query('per_page') int? perPage,
    @Query('search') String? search,
    @Query('shop_id') String? shopId,
  );

  @PUT('/admin/products/{id}/status')
  Future<SimpleApiResponseModel> updateProductStatus(
    @Path('id') String productId,
    @Body() Map<String, dynamic> statusData,
  );

  // Drivers Management
  @GET('/admin/drivers')
  Future<PaginatedResponseModel> getDrivers(
    @Query('page') int? page,
    @Query('per_page') int? perPage,
    @Query('search') String? search,
  );

  @GET('/admin/drivers/{id}')
  Future<BaseApiResponseModel> getDriverDetail(@Path('id') String driverId);

  @GET('/admin/drivers/{id}/orders')
  Future<PaginatedResponseModel> getDriverOrders(
    @Path('id') String driverId,
    @Query('page') int? page,
    @Query('per_page') int? perPage,
  );

  @POST('/admin/drivers/{id}/lock')
  Future<SimpleApiResponseModel> lockDriver(@Path('id') String driverId);

  @POST('/admin/drivers/{id}/unlock')
  Future<SimpleApiResponseModel> unlockDriver(@Path('id') String driverId);

  // Orders Management
  @GET('/admin/orders')
  Future<PaginatedResponseModel> getOrders(
    @Query('page') int? page,
    @Query('per_page') int? perPage,
    @Query('status') String? status,
    @Query('date_from') String? dateFrom,
    @Query('date_to') String? dateTo,
  );

  @POST('/admin/orders')
  Future<BaseApiResponseModel> createOrder(
    @Body() Map<String, dynamic> orderData,
  );

  @GET('/admin/orders/{id}')
  Future<BaseApiResponseModel> getOrderDetail(@Path('id') String orderId);

  @POST('/admin/orders/{id}/status')
  Future<SimpleApiResponseModel> updateOrderStatus(
    @Path('id') String orderId,
    @Body() Map<String, dynamic> statusData,
  );

  @POST('/admin/orders/{id}/reassign')
  Future<SimpleApiResponseModel> reassignOrder(
    @Path('id') String orderId,
    @Body() Map<String, dynamic> reassignData,
  );

  // Configs Management
  @GET('/admin/configs/pricing')
  Future<BaseApiResponseModel> getPricingConfig();

  @PUT('/admin/configs/pricing')
  Future<BaseApiResponseModel> updatePricingConfig(
    @Body() Map<String, dynamic> configData,
  );

  @GET('/admin/configs/surcharges')
  Future<BaseApiResponseModel> getSurchargesConfig();

  @PUT('/admin/configs/surcharges')
  Future<BaseApiResponseModel> updateSurchargesConfig(
    @Body() Map<String, dynamic> configData,
  );

  // Posts Management
  @GET('/admin/posts')
  Future<PaginatedResponseModel> getPosts(
    @Query('page') int? page,
    @Query('per_page') int? perPage,
  );

  @POST('/admin/posts')
  Future<BaseApiResponseModel> createPost(
    @Body() Map<String, dynamic> postData,
  );

  @GET('/admin/posts/{id}')
  Future<BaseApiResponseModel> getPostDetail(@Path('id') String postId);

  @PUT('/admin/posts/{id}')
  Future<BaseApiResponseModel> updatePost(
    @Path('id') String postId,
    @Body() Map<String, dynamic> postData,
  );

  @DELETE('/admin/posts/{id}')
  Future<SimpleApiResponseModel> deletePost(@Path('id') String postId);

  // Stats
  @GET('/admin/stats/summary')
  Future<BaseApiResponseModel> getStatsSummary(
    @Query('date_from') String? dateFrom,
    @Query('date_to') String? dateTo,
  );

  @GET('/admin/stats/by-shop')
  Future<BaseApiResponseModel> getStatsByShop(
    @Query('date_from') String? dateFrom,
    @Query('date_to') String? dateTo,
  );

  @GET('/admin/stats/revenue')
  Future<BaseApiResponseModel> getStatsRevenue(
    @Query('date_from') String? dateFrom,
    @Query('date_to') String? dateTo,
  );
}
