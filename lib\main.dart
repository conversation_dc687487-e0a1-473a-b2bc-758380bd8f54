import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'core/router/app_router.dart';
import 'presentation/providers/theme_provider.dart';

void main() {
  runApp(const ProviderScope(child: MyApp()));
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = AppRouter.router(ref);
    final currentTheme = ref.watch(currentThemeDataProvider);

    return MaterialApp.router(
      title: 'Shipper19+',
      debugShowCheckedModeBanner: false,
      theme: currentTheme, // Sử dụng theme từ provider
      routerConfig: router,
    );
  }
}
