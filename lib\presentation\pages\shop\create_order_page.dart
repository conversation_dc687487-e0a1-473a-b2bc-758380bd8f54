import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../../providers/auth_provider.dart';

class CreateOrderPage extends ConsumerStatefulWidget {
  const CreateOrderPage({super.key});

  @override
  ConsumerState<CreateOrderPage> createState() => _CreateOrderPageState();
}

class _CreateOrderPageState extends ConsumerState<CreateOrderPage> {
  final _formKey = GlobalKey<FormState>();
  final _customerNameController = TextEditingController();
  final _customerPhoneController = TextEditingController();
  final _customerEmailController = TextEditingController();
  final _pickupAddressController = TextEditingController();
  final _dropoffAddressController = TextEditingController();
  final _noteController = TextEditingController();
  final _itemsController = TextEditingController();
  final _codAmountController = TextEditingController();

  String _selectedPaymentMethod = 'COD';
  bool _isUrgent = false;

  @override
  void dispose() {
    _customerNameController.dispose();
    _customerPhoneController.dispose();
    _customerEmailController.dispose();
    _pickupAddressController.dispose();
    _dropoffAddressController.dispose();
    _noteController.dispose();
    _itemsController.dispose();
    _codAmountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);
    final user = authState.user;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Tạo đơn giao hàng'),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Customer Information
              _buildSectionTitle('Thông tin khách hàng'),
              const SizedBox(height: 16),
              TextFormField(
                controller: _customerNameController,
                decoration: const InputDecoration(
                  labelText: 'Tên khách hàng *',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.person),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Vui lòng nhập tên khách hàng';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _customerPhoneController,
                decoration: const InputDecoration(
                  labelText: 'Số điện thoại *',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.phone),
                ),
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Vui lòng nhập số điện thoại';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _customerEmailController,
                decoration: const InputDecoration(
                  labelText: 'Email (tùy chọn)',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.email),
                ),
                keyboardType: TextInputType.emailAddress,
              ),
              const SizedBox(height: 24),

              // Pickup Information
              _buildSectionTitle('Thông tin lấy hàng'),
              const SizedBox(height: 16),
              TextFormField(
                controller: _pickupAddressController,
                decoration: InputDecoration(
                  labelText: 'Địa chỉ lấy hàng *',
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.store),
                  hintText: user?.profile?.shopProfile?.businessAddress ?? 'Nhập địa chỉ lấy hàng',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Vui lòng nhập địa chỉ lấy hàng';
                  }
                  return null;
                },
              ),
              if (user?.profile?.shopProfile?.businessAddress != null)
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: TextButton.icon(
                    onPressed: () {
                      _pickupAddressController.text = user!.profile!.shopProfile!.businessAddress ?? '';
                    },
                    icon: const Icon(Icons.my_location),
                    label: const Text('Sử dụng địa chỉ cửa hàng'),
                  ),
                ),
              const SizedBox(height: 24),

              // Delivery Information
              _buildSectionTitle('Thông tin giao hàng'),
              const SizedBox(height: 16),
              TextFormField(
                controller: _dropoffAddressController,
                decoration: const InputDecoration(
                  labelText: 'Địa chỉ giao hàng *',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.location_on),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Vui lòng nhập địa chỉ giao hàng';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),

              // Order Details
              _buildSectionTitle('Chi tiết đơn hàng'),
              const SizedBox(height: 16),
              TextFormField(
                controller: _itemsController,
                decoration: const InputDecoration(
                  labelText: 'Mô tả hàng hóa *',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.inventory),
                  hintText: 'VD: 2 phần cơm tấm, 1 nước ngọt',
                ),
                maxLines: 3,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Vui lòng mô tả hàng hóa';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _noteController,
                decoration: const InputDecoration(
                  labelText: 'Ghi chú (tùy chọn)',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.note),
                  hintText: 'Ghi chú đặc biệt cho shipper...',
                ),
                maxLines: 2,
              ),
              const SizedBox(height: 24),

              // Payment Information
              _buildSectionTitle('Thông tin thanh toán'),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedPaymentMethod,
                decoration: const InputDecoration(
                  labelText: 'Phương thức thanh toán',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.payment),
                ),
                items: const [
                  DropdownMenuItem(value: 'COD', child: Text('Thu hộ (COD)')),
                  DropdownMenuItem(value: 'PAID', child: Text('Đã thanh toán')),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedPaymentMethod = value!;
                  });
                },
              ),
              if (_selectedPaymentMethod == 'COD') ...[
                const SizedBox(height: 16),
                TextFormField(
                  controller: _codAmountController,
                  decoration: const InputDecoration(
                    labelText: 'Số tiền thu hộ *',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.attach_money),
                    suffixText: 'VNĐ',
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (_selectedPaymentMethod == 'COD' && (value == null || value.isEmpty)) {
                      return 'Vui lòng nhập số tiền thu hộ';
                    }
                    return null;
                  },
                ),
              ],
              const SizedBox(height: 24),

              // Options
              _buildSectionTitle('Tùy chọn'),
              const SizedBox(height: 16),
              CheckboxListTile(
                title: const Text('Đơn hàng khẩn cấp'),
                subtitle: const Text('Phí phụ thu +10.000đ'),
                value: _isUrgent,
                onChanged: (value) {
                  setState(() {
                    _isUrgent = value ?? false;
                  });
                },
                controlAffinity: ListTileControlAffinity.leading,
              ),
              const SizedBox(height: 32),

              // Submit Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _submitOrder,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text(
                    'Tạo đơn giao hàng',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  void _submitOrder() {
    if (_formKey.currentState!.validate()) {
      // Create order logic here
      final orderId = const Uuid().v4();
      
      // Show success dialog
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Thành công'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Đơn hàng đã được tạo thành công!'),
              const SizedBox(height: 8),
              Text('Mã đơn hàng: #${orderId.substring(0, 8).toUpperCase()}'),
              const SizedBox(height: 8),
              const Text('Hệ thống đang tìm shipper phù hợp...'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).pop(); // Go back to shop home
              },
              child: const Text('OK'),
            ),
          ],
        ),
      );
    }
  }
}
