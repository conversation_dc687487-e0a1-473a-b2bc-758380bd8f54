#!/usr/bin/env python3
"""
Script để tạo app icons từ logo.png cho tất cả platforms
Yêu cầu: pip install Pillow
"""

import os
from PIL import Image
import sys

def create_directory(path):
    """T<PERSON><PERSON> thư mục nếu chưa tồn tại"""
    os.makedirs(path, exist_ok=True)

def resize_image(input_path, output_path, size, quality=95):
    """Resize ảnh và lưu với chất lượng cao"""
    try:
        with Image.open(input_path) as img:
            # <PERSON><PERSON><PERSON><PERSON> sang RGBA nếu cần
            if img.mode != 'RGBA':
                img = img.convert('RGBA')
            
            # Resize với chất lượng cao
            resized = img.resize((size, size), Image.Resampling.LANCZOS)
            
            # Lưu file
            if output_path.endswith('.png'):
                resized.save(output_path, 'PNG', optimize=True, quality=quality)
            else:
                # <PERSON><PERSON><PERSON><PERSON> sang RGB cho JPEG
                rgb_img = Image.new('RGB', resized.size, (255, 255, 255))
                rgb_img.paste(resized, mask=resized.split()[-1] if resized.mode == 'RGBA' else None)
                rgb_img.save(output_path, 'JPEG', optimize=True, quality=quality)
            
            print(f"✅ Created: {output_path} ({size}x{size})")
            return True
    except Exception as e:
        print(f"❌ Error creating {output_path}: {e}")
        return False

def generate_android_icons(logo_path, base_path):
    """Tạo Android app icons"""
    print("🤖 Generating Android icons...")
    
    android_sizes = {
        'mipmap-mdpi': 48,
        'mipmap-hdpi': 72,
        'mipmap-xhdpi': 96,
        'mipmap-xxhdpi': 144,
        'mipmap-xxxhdpi': 192,
    }
    
    for folder, size in android_sizes.items():
        folder_path = os.path.join(base_path, 'android', 'app', 'src', 'main', 'res', folder)
        create_directory(folder_path)
        
        output_path = os.path.join(folder_path, 'ic_launcher.png')
        resize_image(logo_path, output_path, size)

def generate_ios_icons(logo_path, base_path):
    """Tạo iOS app icons"""
    print("🍎 Generating iOS icons...")
    
    ios_path = os.path.join(base_path, 'ios', 'Runner', 'Assets.xcassets', 'AppIcon.appiconset')
    create_directory(ios_path)
    
    ios_sizes = [
        (20, '20pt'),
        (29, '29pt'),
        (40, '40pt'),
        (58, '58pt'),
        (60, '60pt'),
        (80, '80pt'),
        (87, '87pt'),
        (120, '120pt'),
        (180, '180pt'),
        (1024, '1024pt'),
    ]
    
    for size, name in ios_sizes:
        output_path = os.path.join(ios_path, f'Icon-App-{name}.png')
        resize_image(logo_path, output_path, size)

def generate_web_icons(logo_path, base_path):
    """Tạo Web app icons"""
    print("🌐 Generating Web icons...")
    
    web_path = os.path.join(base_path, 'web')
    icons_path = os.path.join(web_path, 'icons')
    create_directory(icons_path)
    
    # Favicon
    favicon_path = os.path.join(web_path, 'favicon.png')
    resize_image(logo_path, favicon_path, 32)
    
    # Web icons
    web_sizes = [
        (192, 'Icon-192.png'),
        (512, 'Icon-512.png'),
        (192, 'Icon-maskable-192.png'),
        (512, 'Icon-maskable-512.png'),
    ]
    
    for size, filename in web_sizes:
        output_path = os.path.join(icons_path, filename)
        resize_image(logo_path, output_path, size)

def generate_windows_icons(logo_path, base_path):
    """Tạo Windows app icons"""
    print("🪟 Generating Windows icons...")
    
    windows_path = os.path.join(base_path, 'windows', 'runner', 'resources')
    create_directory(windows_path)
    
    # Windows icon sizes
    windows_sizes = [
        (16, 'app_icon_16.png'),
        (32, 'app_icon_32.png'),
        (48, 'app_icon_48.png'),
        (256, 'app_icon_256.png'),
    ]
    
    for size, filename in windows_sizes:
        output_path = os.path.join(windows_path, filename)
        resize_image(logo_path, output_path, size)

def main():
    """Main function"""
    # Đường dẫn tới logo gốc
    base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    logo_path = os.path.join(base_path, 'assets', 'images', 'logo.png')
    
    print("🎨 Shipper19+ Icon Generator")
    print("=" * 50)
    
    # Kiểm tra logo có tồn tại không
    if not os.path.exists(logo_path):
        print(f"❌ Logo not found: {logo_path}")
        print("Please make sure logo.png exists in assets/images/")
        sys.exit(1)
    
    print(f"📁 Base path: {base_path}")
    print(f"🖼️  Logo path: {logo_path}")
    print()
    
    # Tạo icons cho tất cả platforms
    generate_android_icons(logo_path, base_path)
    print()
    generate_ios_icons(logo_path, base_path)
    print()
    generate_web_icons(logo_path, base_path)
    print()
    generate_windows_icons(logo_path, base_path)
    
    print()
    print("🎉 All icons generated successfully!")
    print("📝 Next steps:")
    print("   1. Run: flutter clean")
    print("   2. Run: flutter build [platform]")
    print("   3. Test the app on different devices")

if __name__ == "__main__":
    main()
