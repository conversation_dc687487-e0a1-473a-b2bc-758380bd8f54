# 🎨 Hướng Dẫn Sử Dụng Theme Shipper19

## 📋 Tổng Quan
Hệ thống theme đã được tối ưu hóa để dễ chỉnh sửa và bảo trì. Tất cả màu sắc được quản lý tập trung trong 2 file chính:

- **`lib/core/constants/app_colors.dart`** - Định nghĩa màu sắc
- **`lib/core/theme/app_theme.dart`** - Cấu hình theme

## 🚀 Cách Thay Đổi Màu Sắc Nhanh

### 1. Thay Đổi Toàn Bộ Theme
Chỉnh sửa **BASE COLORS** trong `app_colors.dart`:

```dart
// Thay đổi màu chính của ứng dụng
static const Color _primaryBase = Color(0xFF4CAF50); // Xanh lá → Đổi thành màu khác

// Thay đổi màu button và accent
static const Color _secondaryBase = Color(0xFF2196F3); // Xanh dương → Đổi thành màu khác
```

### 2. Thay Đổi Màu Theo Component
Chỉnh sửa **COMPONENT COLORS** trong `app_colors.dart`:

```dart
// Chỉ thay đổi màu button
static const Color buttonPrimary = Color(0xFFFF5722); // Đổi thành màu cam

// Chỉ thay đổi màu input
static const Color inputBackground = Color(0xFFF3E5F5); // Đổi thành màu tím nhạt
```

### 3. Thay Đổi Theme Chi Tiết
Chỉnh sửa các method trong `app_theme.dart`:

```dart
// Thay đổi style button
static ElevatedButtonThemeData get _elevatedButtonTheme => ElevatedButtonThemeData(
  style: ElevatedButton.styleFrom(
    // Thêm border radius khác
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
  ),
);
```

## 🎯 Bảng Màu Hiện Tại

| Component | Màu Nền | Màu Chữ | Mã Màu |
|-----------|---------|---------|---------|
| **Nền Chính** | Vàng sáng | Đen xanh | `#FFEB3B` → `#0D2949` |
| **Card/Surface** | Trắng | Đen xanh | `#FFFFFF` → `#0D2949` |
| **Input Field** | Trắng | Đen | `#FFFFFF` → `#000000` |
| **Button Chính** | Đen | Vàng | `#070000` → `#FFEB3B` |
| **Tab Thường** | Vàng nhạt | Đen xanh | `#FFE082` → `#0D2949` |
| **Tab Active** | Xanh lá đậm | Xanh lá đậm | `#225A33` → `#225A33` |
| **Logo** | Transparent | - | `logo.png` (không nền) |

## 🛠️ Cách Sử Dụng Trong Code

### ✅ Đúng - Sử dụng semantic colors:
```dart
// Màu text phù hợp với context
Text('Hello', style: TextStyle(color: AppColors.textOnSurface)) // Trên card trắng
Text('Hello', style: TextStyle(color: AppColors.textOnBackground)) // Trên nền xanh lá

// Màu button theo mục đích
ElevatedButton(
  style: ElevatedButton.styleFrom(backgroundColor: AppColors.buttonPrimary),
  child: Text('Button'),
)
```

### ❌ Tránh - Hardcode màu:
```dart
// Không nên làm như này
Text('Hello', style: TextStyle(color: Color(0xFF000000)))
Container(color: Color(0xFF4CAF50))
```

## 🔄 Quy Trình Thay Đổi Theme

### Bước 1: Xác định loại thay đổi
- **Toàn bộ**: Thay đổi BASE COLORS
- **Một phần**: Thay đổi COMPONENT COLORS  
- **Chi tiết**: Thay đổi trong AppTheme

### Bước 2: Thực hiện thay đổi
1. Mở file tương ứng
2. Thay đổi giá trị màu
3. Hot restart ứng dụng (`R` trong terminal)

### Bước 3: Kiểm tra
- Kiểm tra tất cả màn hình
- Đảm bảo contrast đủ tốt
- Test trên các thiết bị khác nhau

## 📚 Tài Liệu Tham Khảo

### Files Quan Trọng:
- `lib/core/constants/app_colors.dart` - Màu sắc
- `lib/core/theme/app_theme.dart` - Theme configuration
- `lib/main.dart` - Áp dụng theme
- `lib/presentation/widgets/custom_widgets.dart` - Widget tùy chỉnh

### Extension Hữu Ích:
```dart
// Sử dụng extension để lấy màu phù hợp
context.adaptiveTextColor // Tự động chọn màu text phù hợp
context.adaptiveIconColor // Tự động chọn màu icon phù hợp
```

## 🎨 Theme Presets (Sẵn sàng sử dụng)

### Yellow-Black Theme (Hiện tại):
```dart
static const Color _primaryBase = Color(0xFFFFEB3B); // Vàng sáng
static const Color _secondaryBase = Color(0xFF070000); // Đen
static const Color _secondaryDark = Color(0xFF225A33); // Xanh lá đậm (tab active)
```

### Blue Theme:
```dart
static const Color _primaryBase = Color(0xFF2196F3); // Xanh dương
static const Color _secondaryBase = Color(0xFF4CAF50); // Xanh lá
```

### Orange Theme:
```dart
static const Color _primaryBase = Color(0xFFFF9800); // Cam
static const Color _secondaryBase = Color(0xFF3F51B5); // Tím
```

### Purple Theme:
```dart
static const Color _primaryBase = Color(0xFF9C27B0); // Tím
static const Color _secondaryBase = Color(0xFF00BCD4); // Cyan
```

---

## 🎨 **Quick Color Reference - Theme Vàng-Đen Mới**

```
NỀN CHÍNH: Vàng sáng (#FFEB3B) → Chữ đen xanh (#0D2949)
NỀN CARD: Trắng (#FFFFFF) → Chữ đen xanh (#0D2949)
INPUT: Nền trắng (#FFFFFF) → Chữ đen (#000000)
BUTTON: Nền đen (#070000) → Chữ vàng (#FFEB3B)
TAB: Nền vàng nhạt (#FFE082) → Chữ đen xanh (#0D2949)
TAB ACTIVE: Nền xanh lá đậm (#225A33) → Chữ trắng (#FFFFFF)
LOGO: Ảnh logo.png không nền (transparent)
```

**💡 Lưu ý**: Sau khi thay đổi màu sắc, luôn thực hiện hot restart (`R`) để áp dụng thay đổi!
