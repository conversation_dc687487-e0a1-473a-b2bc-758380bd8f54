import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import '../models/api_response_model.dart';

part 'customer_api_service.g.dart';

@RestApi()
abstract class CustomerApiService {
  factory CustomerApiService(Dio dio, {String baseUrl}) = _CustomerApiService;

  // Shops
  @GET('/customers/shops')
  Future<PaginatedResponseModel> getShops(
    @Query('page') int? page,
    @Query('per_page') int? perPage,
    @Query('search') String? search,
    @Query('category_id') int? categoryId,
  );

  @GET('/customers/shops/{id}')
  Future<BaseApiResponseModel> getShopDetail(@Path('id') String shopId);

  @GET('/customers/shops/{id}/products')
  Future<PaginatedResponseModel> getShopProducts(
    @Path('id') String shopId,
    @Query('page') int? page,
    @Query('per_page') int? perPage,
    @Query('search') String? search,
    @Query('category_id') int? categoryId,
    @Query('min_price') double? minPrice,
    @Query('max_price') double? maxPrice,
  );

  // Categories
  @GET('/customers/categories')
  Future<BaseApiResponseModel> getCategories(
    @Query('page') int? page,
    @Query('per_page') int? perPage,
  );

  // Products
  @GET('/customers/products')
  Future<PaginatedResponseModel> getProducts(
    @Query('page') int? page,
    @Query('per_page') int? perPage,
    @Query('search') String? search,
    @Query('category_id') int? categoryId,
    @Query('shop_id') int? shopId,
    @Query('min_price') double? minPrice,
    @Query('max_price') double? maxPrice,
  );

  // Products by Category
  @GET('/customers/categories/{id}/products')
  Future<BaseApiResponseModel> getProductsByCategory(
    @Path('id') int categoryId,
    @Query('page') int? page,
    @Query('per_page') int? perPage,
  );

  @GET('/customers/products/{id}')
  Future<BaseApiResponseModel> getProductDetail(@Path('id') String productId);

  // Orders
  @POST('/customers/orders')
  Future<BaseApiResponseModel> createOrder(
    @Body() Map<String, dynamic> orderData,
  );

  @GET('/customers/orders')
  Future<PaginatedResponseModel> getOrders(
    @Query('page') int? page,
    @Query('per_page') int? perPage,
    @Query('status') String? status,
  );

  @GET('/customers/orders/{id}')
  Future<BaseApiResponseModel> getOrderDetail(@Path('id') String orderId);

  @POST('/customers/orders/{id}/cancel')
  Future<SimpleApiResponseModel> cancelOrder(
    @Path('id') String orderId,
    @Body() Map<String, dynamic> cancelData,
  );

  @POST('/customers/orders/{id}/rate')
  Future<SimpleApiResponseModel> rateOrder(
    @Path('id') String orderId,
    @Body() Map<String, dynamic> ratingData,
  );

  // Unified Search
  @GET('/customers/search')
  Future<BaseApiResponseModel> search(
    @Query('type') String type, // 'products' | 'shops' | 'both'
    @Query('q') String? q,
    @Query('category_id') int? categoryId,
    @Query('latitude') double? latitude,
    @Query('longitude') double? longitude,
    @Query('radius') double? radius,
    @Query('per_page') int? perPage,
    // Product filters
    @Query('shop_id') int? shopId,
    @Query('min_price') double? minPrice,
    @Query('max_price') double? maxPrice,
    // Shop filters
    @Query('min_rating') double? minRating,
    @Query('max_delivery_fee') double? maxDeliveryFee,
    @Query('max_minimum_order') double? maxMinimumOrder,
    @Query('is_open') bool? isOpen,
    // Sorting
    @Query('sort_by') String? sortBy,
    @Query('sort_order') String? sortOrder,
  );

  // Reviews
  @POST('/customers/products/{id}/review')
  Future<BaseApiResponseModel> postProductReview(
    @Path('id') String productId,
    @Body() Map<String, dynamic> body,
  );

  @POST('/customers/shops/{id}/review')
  Future<BaseApiResponseModel> postShopReview(
    @Path('id') String shopId,
    @Body() Map<String, dynamic> body,
  );

  @GET('/customers/products/{id}/reviews')
  Future<BaseApiResponseModel> getProductReviews(
    @Path('id') String productId,
    @Query('page') int? page,
    @Query('per_page') int? perPage,
  );

  @GET('/customers/shops/{id}/reviews')
  Future<BaseApiResponseModel> getShopReviews(
    @Path('id') String shopId,
    @Query('page') int? page,
    @Query('per_page') int? perPage,
  );
}
