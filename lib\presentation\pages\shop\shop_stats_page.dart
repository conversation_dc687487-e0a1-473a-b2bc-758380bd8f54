import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ShopStatsPage extends ConsumerStatefulWidget {
  const ShopStatsPage({super.key});

  @override
  ConsumerState<ShopStatsPage> createState() => _ShopStatsPageState();
}

class _ShopStatsPageState extends ConsumerState<ShopStatsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedPeriod = 'today';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Thống kê'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Tổng quan'),
            Tab(text: 'Doanh thu'),
            Tab(text: 'Sản phẩm'),
          ],
        ),
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              setState(() {
                _selectedPeriod = value;
              });
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'today', child: Text('Hôm nay')),
              const PopupMenuItem(value: 'week', child: Text('Tuần này')),
              const PopupMenuItem(value: 'month', child: Text('Tháng này')),
              const PopupMenuItem(value: 'year', child: Text('Năm này')),
            ],
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(_getPeriodText()),
                  const Icon(Icons.arrow_drop_down),
                ],
              ),
            ),
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildRevenueTab(),
          _buildProductsTab(),
        ],
      ),
    );
  }

  String _getPeriodText() {
    switch (_selectedPeriod) {
      case 'today':
        return 'Hôm nay';
      case 'week':
        return 'Tuần này';
      case 'month':
        return 'Tháng này';
      case 'year':
        return 'Năm này';
      default:
        return 'Hôm nay';
    }
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Key Metrics
          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  'Đơn hàng',
                  '12',
                  '+8.3%',
                  Icons.receipt_long,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildMetricCard(
                  'Doanh thu',
                  '2.4M',
                  '+12.5%',
                  Icons.attach_money,
                  Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  'Khách hàng',
                  '8',
                  '+5.2%',
                  Icons.people,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildMetricCard(
                  'Đánh giá TB',
                  '4.5⭐',
                  '+0.2',
                  Icons.star,
                  Colors.purple,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Recent Activity
          const Text(
            'Hoạt động gần đây',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildActivityList(),
          const SizedBox(height: 24),

          // Top Products
          const Text(
            'Sản phẩm bán chạy',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildTopProductsList(),
        ],
      ),
    );
  }

  Widget _buildRevenueTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Revenue Summary
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.green, Colors.green.withOpacity(0.8)],
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Tổng doanh thu',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  '2.400.000đ',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(Icons.trending_up, color: Colors.white70, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      '+12.5% so với ${_getPreviousPeriod()}',
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Revenue Breakdown
          const Text(
            'Chi tiết doanh thu',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildRevenueBreakdown(),
          const SizedBox(height: 24),

          // Revenue Chart Placeholder
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.bar_chart, size: 48, color: Colors.grey),
                  SizedBox(height: 8),
                  Text(
                    'Biểu đồ doanh thu',
                    style: TextStyle(color: Colors.grey),
                  ),
                  Text(
                    'Sẽ được cập nhật trong phiên bản tiếp theo',
                    style: TextStyle(color: Colors.grey, fontSize: 12),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product Stats
          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  'Tổng sản phẩm',
                  '25',
                  '+3',
                  Icons.inventory,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildMetricCard(
                  'Đã bán',
                  '156',
                  '+23',
                  Icons.shopping_cart,
                  Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Top Selling Products
          const Text(
            'Sản phẩm bán chạy nhất',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildTopSellingProducts(),
          const SizedBox(height: 24),

          // Low Stock Alert
          const Text(
            'Cảnh báo tồn kho',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.orange[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.orange[200]!),
            ),
            child: const Row(
              children: [
                Icon(Icons.warning, color: Colors.orange),
                SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Tất cả sản phẩm đều còn hàng',
                    style: TextStyle(color: Colors.orange),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricCard(String title, String value, String change, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Icon(icon, color: color, size: 24),
              Text(
                change,
                style: TextStyle(
                  color: Colors.green,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityList() {
    final activities = [
      {'time': '10:30', 'action': 'Đơn hàng mới #001234', 'amount': '+71.000đ'},
      {'time': '09:45', 'action': 'Đơn hàng hoàn thành #001233', 'amount': '+66.000đ'},
      {'time': '09:15', 'action': 'Sản phẩm mới được duyệt', 'amount': ''},
      {'time': '08:30', 'action': 'Đơn hàng mới #001232', 'amount': '+97.000đ'},
    ];

    return Column(
      children: activities.map((activity) => ListTile(
        leading: CircleAvatar(
          backgroundColor: Colors.blue[100],
          child: const Icon(Icons.notifications, color: Colors.blue, size: 20),
        ),
        title: Text(activity['action']!),
        subtitle: Text(activity['time']!),
        trailing: activity['amount']!.isNotEmpty
            ? Text(
                activity['amount']!,
                style: const TextStyle(
                  color: Colors.green,
                  fontWeight: FontWeight.bold,
                ),
              )
            : null,
      )).toList(),
    );
  }

  Widget _buildTopProductsList() {
    final products = [
      {'name': 'Cơm Tấm Sườn Nướng', 'sold': '23', 'revenue': '1.035.000đ'},
      {'name': 'Cơm Tấm Bì Chả', 'sold': '18', 'revenue': '630.000đ'},
      {'name': 'Bánh Mì Thịt Nướng', 'sold': '15', 'revenue': '375.000đ'},
    ];

    return Column(
      children: products.map((product) => ListTile(
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(Icons.fastfood),
        ),
        title: Text(product['name']!),
        subtitle: Text('Đã bán: ${product['sold']}'),
        trailing: Text(
          product['revenue']!,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.green,
          ),
        ),
      )).toList(),
    );
  }

  Widget _buildRevenueBreakdown() {
    return Column(
      children: [
        _buildRevenueRow('Doanh thu từ món ăn', '2.100.000đ', '87.5%'),
        _buildRevenueRow('Phí giao hàng', '240.000đ', '10%'),
        _buildRevenueRow('Phí dịch vụ', '60.000đ', '2.5%'),
      ],
    );
  }

  Widget _buildRevenueRow(String label, String amount, String percentage) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(label),
          ),
          Text(
            '$amount ($percentage)',
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  Widget _buildTopSellingProducts() {
    final products = [
      {'name': 'Cơm Tấm Sướn Nướng', 'sold': '23', 'percentage': '85%'},
      {'name': 'Cơm Tấm Bì Chả', 'sold': '18', 'percentage': '67%'},
      {'name': 'Bánh Mì Thịt Nướng', 'sold': '15', 'percentage': '55%'},
    ];

    return Column(
      children: products.map((product) => Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    product['name']!,
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Đã bán: ${product['sold']}',
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                ],
              ),
            ),
            Text(
              product['percentage']!,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
          ],
        ),
      )).toList(),
    );
  }

  String _getPreviousPeriod() {
    switch (_selectedPeriod) {
      case 'today':
        return 'hôm qua';
      case 'week':
        return 'tuần trước';
      case 'month':
        return 'tháng trước';
      case 'year':
        return 'năm trước';
      default:
        return 'hôm qua';
    }
  }
}
