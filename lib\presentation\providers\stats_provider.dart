import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/models/driver_stats_model.dart';
import '../../domain/usecases/usecase_providers.dart';
import '../../domain/usecases/driver_usecases.dart';

// Stats State
class StatsState {
  final DriverStatsModel? stats;
  final bool isLoading;
  final String? error;

  const StatsState({
    this.stats,
    this.isLoading = false,
    this.error,
  });

  StatsState copyWith({
    DriverStatsModel? stats,
    bool? isLoading,
    String? error,
  }) {
    return StatsState(
      stats: stats ?? this.stats,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// Stats Notifier
class StatsNotifier extends StateNotifier<StatsState> {
  final GetDriverStatsUseCase getDriverStatsUseCase;

  StatsNotifier({
    required this.getDriverStatsUseCase,
  }) : super(const StatsState());

  Future<void> loadStats({String? date}) async {
    state = state.copyWith(isLoading: true, error: null);
    
    final result = await getDriverStatsUseCase(date);
    
    result.fold(
      (failure) {
        state = state.copyWith(
          isLoading: false,
          error: failure.message,
        );
      },
      (stats) {
        state = state.copyWith(
          stats: stats,
          isLoading: false,
          error: null,
        );
      },
    );
  }

  Future<void> loadTodayStats() async {
    final today = DateTime.now();
    final dateString = '${today.year}-${today.month.toString().padLeft(2, '0')}-${today.day.toString().padLeft(2, '0')}';
    await loadStats(date: dateString);
  }
}

// Stats Provider
final statsProvider = StateNotifierProvider<StatsNotifier, StatsState>((ref) {
  return StatsNotifier(
    getDriverStatsUseCase: ref.read(getDriverStatsUseCaseProvider),
  );
});
