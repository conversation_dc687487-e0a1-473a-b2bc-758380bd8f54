import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:geolocator/geolocator.dart';
import '../../core/services/location_service.dart';

// Location State
class LocationState {
  final Position? currentPosition;
  final bool isLoading;
  final String? error;
  final bool hasPermission;
  final bool isLocationServiceEnabled;

  const LocationState({
    this.currentPosition,
    this.isLoading = false,
    this.error,
    this.hasPermission = false,
    this.isLocationServiceEnabled = false,
  });

  LocationState copyWith({
    Position? currentPosition,
    bool? isLoading,
    String? error,
    bool? hasPermission,
    bool? isLocationServiceEnabled,
  }) {
    return LocationState(
      currentPosition: currentPosition ?? this.currentPosition,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      hasPermission: hasPermission ?? this.hasPermission,
      isLocationServiceEnabled:
          isLocationServiceEnabled ?? this.isLocationServiceEnabled,
    );
  }

  // Helper getters
  double get latitude =>
      currentPosition?.latitude ?? 10.8231; // Default to Ho Chi Minh City
  double get longitude => currentPosition?.longitude ?? 106.6297;
  bool get hasLocation => currentPosition != null;
}

// Location Notifier
class LocationNotifier extends StateNotifier<LocationState> {
  LocationNotifier() : super(const LocationState()) {
    _initializeLocation();
  }

  Future<void> _initializeLocation() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Check if location service is enabled
      final isServiceEnabled = await LocationService.isLocationServiceEnabled();

      // Check permission
      final hasPermission = await LocationService.hasLocationPermission();

      state = state.copyWith(
        isLocationServiceEnabled: isServiceEnabled,
        hasPermission: hasPermission,
      );

      if (hasPermission && isServiceEnabled) {
        await getCurrentLocation();
      } else {
        // Use default location
        final defaultPosition = LocationService.getDefaultPosition();
        state = state.copyWith(
          currentPosition: defaultPosition,
          isLoading: false,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
        currentPosition: LocationService.getDefaultPosition(),
      );
    }
  }

  Future<void> getCurrentLocation() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final position = await LocationService.getCurrentPosition();

      if (position != null) {
        state = state.copyWith(
          currentPosition: position,
          isLoading: false,
          hasPermission: true,
        );
      } else {
        // Fallback to default location
        final defaultPosition = LocationService.getDefaultPosition();
        state = state.copyWith(
          currentPosition: defaultPosition,
          isLoading: false,
          error: 'Không thể lấy vị trí hiện tại, sử dụng vị trí mặc định',
        );
      }
    } catch (e) {
      final defaultPosition = LocationService.getDefaultPosition();
      state = state.copyWith(
        currentPosition: defaultPosition,
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<void> requestLocationPermission() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final status = await LocationPermissionHelper.getPermissionStatus();

      switch (status) {
        case LocationPermissionStatus.granted:
          await getCurrentLocation();
          break;

        case LocationPermissionStatus.denied:
          final granted =
              await LocationPermissionHelper.requestLocationPermission();
          if (granted) {
            await getCurrentLocation();
          } else {
            state = state.copyWith(
              isLoading: false,
              hasPermission: false,
              error: 'Quyền truy cập vị trí bị từ chối',
            );
          }
          break;

        case LocationPermissionStatus.deniedForever:
          state = state.copyWith(
            isLoading: false,
            hasPermission: false,
            error: 'Quyền truy cập vị trí bị từ chối vĩnh viễn',
          );
          break;

        case LocationPermissionStatus.serviceDisabled:
          state = state.copyWith(
            isLoading: false,
            isLocationServiceEnabled: false,
            error: 'Dịch vụ vị trí bị tắt',
          );
          break;
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  void updatePosition(Position position) {
    state = state.copyWith(currentPosition: position);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  // Calculate distance to a point
  double calculateDistanceTo(double latitude, double longitude) {
    return LocationService.calculateDistance(
      state.latitude,
      state.longitude,
      latitude,
      longitude,
    );
  }

  // Format distance for display
  String formatDistance(double distanceInKm) {
    return LocationService.formatDistance(distanceInKm);
  }

  // Get formatted current address using geocoding
  Future<String> getCurrentAddress() async {
    if (state.hasLocation) {
      return await LocationService.getShortAddressFromCoordinates(
        state.latitude,
        state.longitude,
      );
    }
    return 'Thành phố Hồ Chí Minh'; // Default address
  }

  // Get current address synchronously (cached or default)
  String getCurrentAddressSync() {
    if (state.hasLocation) {
      // Return a simple format for immediate display
      return 'Vị trí hiện tại';
    }
    return 'Thành phố Hồ Chí Minh'; // Default address
  }
}

// Provider
final locationProvider = StateNotifierProvider<LocationNotifier, LocationState>(
  (ref) {
    return LocationNotifier();
  },
);

// Helper providers
final currentPositionProvider = Provider<Position?>((ref) {
  return ref.watch(locationProvider).currentPosition;
});

final hasLocationPermissionProvider = Provider<bool>((ref) {
  return ref.watch(locationProvider).hasPermission;
});

final currentLatLngProvider = Provider<(double, double)>((ref) {
  final locationState = ref.watch(locationProvider);
  return (locationState.latitude, locationState.longitude);
});

// Provider for calculating distance to restaurants
final restaurantDistanceProvider = Provider.family<double, (double, double)>((
  ref,
  restaurantLatLng,
) {
  final locationNotifier = ref.read(locationProvider.notifier);
  return locationNotifier.calculateDistanceTo(
    restaurantLatLng.$1,
    restaurantLatLng.$2,
  );
});

// Provider for formatted distance
final formattedDistanceProvider = Provider.family<String, double>((
  ref,
  distanceInKm,
) {
  final locationNotifier = ref.read(locationProvider.notifier);
  return locationNotifier.formatDistance(distanceInKm);
});
