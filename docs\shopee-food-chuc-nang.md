## Tài liệu mô tả kỹ chức năng Shopee Food

### 1) <PERSON><PERSON><PERSON> tiêu sản phẩm
- <PERSON><PERSON> cấp nền tảng đặt món ăn n<PERSON> chóng, tiệ<PERSON> lợi, an toàn và đáng tin cậy.
- Tối ưu trải nghiệm cho ba nhóm chính: <PERSON><PERSON><PERSON><PERSON> dù<PERSON> (kh<PERSON><PERSON> hàng), <PERSON><PERSON><PERSON> tá<PERSON> hà<PERSON>, <PERSON><PERSON><PERSON> xế giao hàng; và hỗ trợ vận hành cho Quản trị hệ thống.

### 2) Phạm vi & giả định
- Phạm vi phiên bản đầu: Web và Mobile (iOS/Android) người dùng; portal web cho Nhà hàng; app/portal cho Tài xế.
- Hỗ trợ các thành phố lớn, thanh toán COD, ví điện tử, thẻ nội địa/ quốc tế; quản lý khu<PERSON>ến mãi, voucher.
- <PERSON><PERSON><PERSON> định: t<PERSON>ch hợp dịch vụ định vị, b<PERSON><PERSON> đ<PERSON>, c<PERSON><PERSON> <PERSON><PERSON> to<PERSON>, dịch vụ g<PERSON><PERSON> thông bá<PERSON> (Push/Email/SMS), chat realtime.

### 3) Loại tài khoản & vai trò
- Shop: tạo đơn giao hàng (nhập địa chỉ gửi/nhận), quản lý và đăng sản phẩm (cần admin duyệt), theo dõi đơn, xem lịch sử, xem bài viết từ admin, xem thống kê theo ngày.
- Shipper: nhận đơn theo thời gian thực (chỉ đơn chưa ai nhận), tối đa 3 đơn cùng lúc, cập nhật trạng thái (đang lấy hàng → đang giao → hoàn thành), nhận thông báo, xem thống kê cá nhân, được đánh giá bởi KH & Shop.
- Khách hàng: xem thông tin, duyệt/tìm kiếm sản phẩm, đặt đơn (đăng nhập trước hoặc nhập thông tin người nhận ở bước đặt), xem tiến độ và lịch sử đơn đã đặt.
- Quản trị (Admin/CS): duyệt sản phẩm, đăng bài viết, cấu hình/giám sát, thống kê theo ngày, xử lý chuyển đơn khi treo >2 phút.

### 4) Luồng người dùng tổng quát theo loại tài khoản
- Shop: Đăng nhập → Tạo đơn (nhập địa chỉ gửi/nhận) → Chọn sản phẩm trong gian hàng → Xác nhận đơn → Theo dõi trạng thái → Lịch sử → Thống kê.
- Shipper: Đăng nhập → Xem danh sách đơn mới → Xem chi tiết → Nhận đơn (nếu còn trống) → Đang lấy hàng → Đang giao → Hoàn thành → Thống kê.
- Khách hàng: Đăng nhập/đặt nhanh → Duyệt/tìm kiếm sản phẩm → Lên đơn (nhập thông tin người nhận nếu chưa đăng nhập) → Theo dõi đơn → Lịch sử.

#### Wireframe luồng đặt món (Mobile)

Sơ đồ luồng (Mermaid)

```mermaid
flowchart TD
  A[Trang chủ / Đăng nhập] --> B[Tìm kiếm / Danh mục]
  B --> C[Trang Nhà hàng]
  C --> D[Chi tiết món / Chọn tuỳ chọn]
  D --> E[Giỏ hàng]
  E --> F[Áp voucher]
  F --> G[Chọn địa chỉ giao]
  G --> H[Chọn phương thức thanh toán]
  H --> I[Xác nhận đặt hàng]
  I --> J[Theo dõi đơn]
  J --> K[Đánh giá]

  H -->|Thanh toán lỗi| F
  I -->|Không có tài xế| E
```

Wireframe khung màn hình (ASCII – mobile)

- Màn hình Trang chủ

      ┌──────────────────────────┐
      │  Tìm kiếm… (search bar)  │
      ├──────── Banner ──────────┤
      │  Danh mục / Gần bạn      │
      │  Nhà hàng nổi bật …      │
      └──────────────────────────┘

- Trang Nhà hàng

      ┌──────────────────────────┐
      │  Tên NH   ★4.6  ETA 25'  │
      ├ Tabs: Menu | Review | Info│
      │ [Món A]  45K   [+]       │
      │ [Món B]  35K   [+]       │
      └──────────────────────────┘

- Chi tiết món / Tùy chọn

      ┌──────────────────────────┐
      │ Món A        45K         │
      │ Size: (S) (M) (L)        │
      │ Topping: [..] [..]       │
      │ Ghi chú: ____________    │
      │ [-] 1 [+]   [Thêm vào giỏ]
      └──────────────────────────┘

- Giỏ hàng

      ┌──────────────────────────┐
      │ Món A x1   45K   [Sửa]   │
      │ Phí ship    15K          │
      │ Voucher: [Nhập/Chọn]     │
      │ Tổng:        60K         │
      │   [Tiếp tục đặt hàng]    │
      └──────────────────────────┘

- Chọn địa chỉ & Thanh toán

      ┌──────────────────────────┐
      │ Địa chỉ: [Chọn / Thêm]  │
      │ Thanh toán: (COD) (Ví)   │
      │ (Thẻ) …                  │
      │ [Đặt hàng]               │
      └──────────────────────────┘

- Theo dõi đơn

      ┌──────────────────────────┐
      │ Bản đồ + vị trí TX       │
      │ Trạng thái: …            │
      │ ETA: 18'  [Chat] [Gọi]   │
      └──────────────────────────┘

Ghi chú UI/UX quan trọng
- Hiển thị breakdown giá, tổng cập nhật realtime khi đổi món/voucher.
- Validation topping/biến thể ngay tại màn chi tiết món, báo lỗi rõ ràng.
- Tối ưu CTA: ở giỏ hàng/checkout luôn hiện nổi bật, cố định cuối màn hình.
- Trạng thái đơn cập nhật realtime; fallback sang polling nếu mất kết nối.


#### Cây luồng & screen_id
- Nội dung đã được tách sang tài liệu riêng: [docs/shopee-food-screens.md](./shopee-food-screens.md)
- Bao gồm: Cây luồng màn hình chi tiết, bảng screen_id ↔ Screen Name, và sơ đồ Mermaid.



#### Bảng screen_id & Mermaid
- Nội dung đã được tách sang tài liệu riêng: [docs/shopee-food-screens.md](./shopee-food-screens.md)
- Bao gồm: bảng screen_id ↔ Screen Name và sơ đồ Mermaid dạng cây.

### 5) Chức năng theo loại tài khoản

- Shop
  - Tạo đơn giao hàng: nhập địa chỉ gửi và nhận; chọn sản phẩm trong gian; xác nhận tạo đơn.
  - Quản lý sản phẩm: tự thêm/sửa/xoá; trạng thái chờ duyệt; admin duyệt xong mới hiển thị; có thể huỷ đăng.
  - Theo dõi trạng thái đơn: đơn chờ ship nhận, đơn đang giao, đơn hoàn thành/huỷ.
  - Lịch sử đơn: xem trạng thái các đơn đã đặt, các đơn đã huỷ.
  - Xem bài viết từ Admin.
  - Thống kê đơn hàng của shop theo ngày (công nợ: không có; chỉ thống kê).

- Shipper
  - Xem danh sách đơn mới theo thời gian thực; chỉ thấy đơn chưa ai nhận; khi một shipper nhận, đơn ẩn khỏi list người khác.
  - Xem chi tiết đơn trước khi nhận: khoảng cách, phí ship, phụ phí kèm theo.
  - Nhận đơn: chỉ khi đơn chưa ai nhận; tối đa 3 đơn cùng lúc.
  - Cập nhật đơn: Đang lấy hàng → Đang giao → Hoàn thành; nhấn Hoàn thành để tính phí ship cuối cùng.
  - Nhận thông báo: khi có đơn mới; nếu đơn treo > 2 phút tự động chuyển đơn cho shipper khác.
  - Thống kê cá nhân: tổng số đơn trong ngày, tổng số tiền nhận được, lịch sử đơn đã chạy.
  - Đánh giá: nhận đánh giá sao từ Khách hàng & Shop cho mỗi đơn.

- Khách hàng
  - Xem thông tin trên app; xem danh sách sản phẩm & tìm kiếm.
  - Đặt đơn: yêu cầu đăng nhập trước hoặc sau khi lên đơn thì điền thông tin người nhận.
  - Xem đơn đã đặt: lịch sử và tiến độ đơn.

- Admin/CS
  - Duyệt sản phẩm của shop (đã duyệt mới được đăng lên).
  - Đăng bài viết (hiển thị cho Shop).
  - Thống kê theo ngày; giám sát đơn treo > 2 phút để chuyển đơn.

### 6) Chức năng Nhà hàng (Portal NH)
- Quản lý cửa hàng
  - Hồ sơ: tên, địa chỉ, giờ mở, khu vực phục vụ, SLA chuẩn bị.
  - Bật/tắt nhận đơn theo khung giờ; tạm đóng.
- Quản lý menu
  - Danh mục, món, biến thể/topping; giá; tồn kho/bán hết.
  - Lịch khuyến mãi, combo, giá theo khung giờ.
- Nhận và xử lý đơn
  - Danh sách đơn chờ xác nhận/đang chuẩn bị/đã hoàn tất.
  - Cập nhật trạng thái; in hóa đơn; thời gian dự kiến (prep time).
- Báo cáo & thanh toán
  - Doanh thu, số đơn, top món, phản hồi.
  - Đối soát, sao kê, cấu hình tài khoản nhận tiền.

### 7) Chức năng Tài xế (App/Portal TX)
- Hồ sơ & trạng thái làm việc: online/offline, khu vực, phương tiện.
- Nhận cuốc: chấp nhận/ từ chối trong thời hạn; điểm uy tín.
- Điều hướng: tới NH, tới khách; tích hợp bản đồ.
- Cập nhật trạng thái: đã tới NH, đã nhận món, đang giao, đã giao.
- Thanh toán: thu COD, đối soát ví tài xế; lịch sử cuốc; hỗ trợ.

### 8) Quản trị & CS
- Quản trị hệ thống: danh mục toàn cục, cấu hình phí, vùng phục vụ, khuyến mãi hệ thống.
- Kiểm duyệt nội dung: ảnh, review, menu.
- Quản lý khiếu nại/hoàn tiền: SLA, phân công, macro trả lời.
- Báo cáo: tăng trưởng, retention, đơn theo giờ/khu vực, tỉ lệ hủy.

### 9) Thanh toán & Khuyến mãi
- Cổng thanh toán: tích hợp ví, thẻ (3DS), COD.
- Quy tắc voucher/khuyến mãi
  - Loại: giảm %/số tiền cố định/miễn phí ship/đồng giá/combo.
  - Điều kiện: min order, khung giờ, khu vực, người dùng mới, số lần dùng, stackable.
  - Chống lạm dụng: ràng buộc thiết bị, tài khoản, phương thức thanh toán, velocity limit.

### 10) Định vị, phí, ETA
- Ước tính phí ship theo khoảng cách/khu vực/điều kiện thời tiết/giờ cao điểm.
- Tính ETA dựa trên: khoảng cách, thời gian chuẩn bị NH, lịch sử tắc đường.
- Geofencing: ràng buộc vùng phục vụ, cấm đơn ngoài vùng.

### 11) Thông báo & Liên lạc
- Shop: thông báo khi đơn được shipper nhận, khi giao xong/huỷ; thông báo bài viết mới từ Admin.
- Shipper: thông báo đơn mới; cảnh báo đơn treo > 2 phút; thay đổi trạng thái đơn; nhắc giới hạn 3 đơn.
- Khách hàng: thông báo xác nhận đặt đơn, thay đổi trạng thái, hoàn thành/huỷ.
- Admin: có thể phát bài viết/announcement.

### 12) Bảo mật & Quyền riêng tư
- Bảo vệ dữ liệu cá nhân: mã hóa at rest/in transit, tuân thủ luật địa phương.
- Token hóa thẻ, PCI-DSS (nếu lưu token); 2FA cho portal NH/TX/ADM.
- RBAC: phân quyền KH/NH/TX/ADM; audit log cho tác vụ nhạy cảm.

### 13) Yêu cầu phi chức năng
- Hiệu năng: thời gian tải trang chủ < 2s 95th, tìm kiếm < 500ms 95th (cache khi phù hợp).
- Tính sẵn sàng: >= 99.9%; graceful degradation khi dịch vụ phụ trợ lỗi.
- Khả năng mở rộng: scale theo khu vực; kiến trúc service tách rời.
- Khả dụng ngoại tuyến (limited): lưu địa chỉ/giỏ hàng tạm thời cục bộ.
- Khả năng quan sát: logging có cấu trúc, tracing, metrics (APM).
- i18n/a11y: hỗ trợ đa ngôn ngữ; tiêu chuẩn truy cập cơ bản.

### 14) Model dữ liệu (mức khái quát)
- User: id, profile, role in {SHOP, SHIPPER, CUSTOMER, ADMIN}.
- Shop: id, name, address, contact.
- Product: id, shop_id, name, desc, price, status in {DRAFT, PENDING_APPROVAL, PUBLISHED, UNPUBLISHED}.
- Order: id, created_by (shop_id|customer_id), items[{product_id, qty, price}], pickup_address, dropoff_address, fee_breakdown, status, timeline[], assigned_driver_id.
- Driver: id, name, rating, active_order_count.
- Post: id, title, content, audience in {SHOP, ALL}, created_by (admin_id).

### 15) API (khung endpoint tham khảo)
- Shop
  - POST /shops/orders (tạo đơn: địa chỉ gửi/nhận, sản phẩm)
  - GET /shops/orders?status=… (theo dõi/truy vấn)
  - GET /shops/orders/{id}
  - GET /shops/products (liệt kê của shop)
  - POST /shops/products (tạo mới) / PUT /shops/products/{id} / DELETE /shops/products/{id}
  - POST /shops/products/{id}/publish (yêu cầu duyệt) / POST /shops/products/{id}/unpublish
- Shipper
  - GET /drivers/orders/available (đơn mới)
  - GET /drivers/orders/{id} (chi tiết trước khi nhận)
  - POST /drivers/orders/{id}/accept (nhận đơn; idempotent)
  - POST /drivers/orders/{id}/status (PICKING/DELIVERING/COMPLETED)
- Khách hàng
  - GET /products?search=…
  - POST /orders (đặt đơn; nếu chưa đăng nhập vẫn cho đặt kèm thông tin nhận)
  - GET /orders/{id} (theo dõi)
  - GET /orders?mine=1 (lịch sử)
- Admin
  - POST /admin/products/{id}/approve | /reject
  - POST /admin/posts (bài viết) / GET /admin/posts

### 15.1) API cho App (Mobile)

- Auth & Versioning
  - Prefix: /api/v1
  - Auth: JWT/Bearer cho người dùng đăng nhập; cho phép tạo đơn guest với thông tin nhận đầy đủ (customer)

- Shop (vai trò SHOP)
  - POST /shops/orders { pickup_address, dropoff_address, items[], surcharges[] } → tạo đơn
  - GET  /shops/orders?status=&page= → theo dõi/truy vấn
  - GET  /shops/orders/{id}
  - GET  /shops/posts → bài viết từ admin
  - GET  /shops/stats?date=YYYY-MM-DD → thống kê theo ngày

- Shipper (vai trò SHIPPER)
  - GET  /drivers/orders/available → danh sách đơn mới (realtime/polling)
  - GET  /drivers/orders/{id} → chi tiết trước khi nhận
  - POST /drivers/orders/{id}/accept → nhận đơn (idempotent)
  - POST /drivers/orders/{id}/status { status: PICKING|DELIVERING|COMPLETED }
  - GET  /drivers/stats?date=YYYY-MM-DD → thống kê cá nhân

- Khách hàng (vai trò CUSTOMER)
  - GET  /products?search=&page=
  - POST /orders { receiver_info?, items[], pickup_address?, dropoff_address } → đặt đơn (cho phép guest nếu đủ thông tin)
  - GET  /orders/{id} → theo dõi đơn
  - GET  /orders?mine=1 → lịch sử đơn

- Notifications
  - Push sự kiện khi: đơn mới (shipper), trạng thái đơn thay đổi (shop/customer), bài viết mới (shop)

- Lỗi & mã trả về (ví dụ)
  - 409 CONFLICT: DRIVER_LIMIT_REACHED (shipper > 3 đơn); ORDER_ALREADY_TAKEN; INVALID_STATUS_TRANSITION
  - 422 UNPROCESSABLE_ENTITY: địa chỉ không hợp lệ / thiếu thông tin bắt buộc

  - POST /admin/orders/{id}/reassign (chuyển đơn khi treo)

### 16) Luồng trạng thái đơn hàng (điều chỉnh)
- NEW (Shop tạo) -> PENDING_DRIVER (chờ ship nhận) -> PICKING (đang lấy hàng) -> DELIVERING (đang giao) -> COMPLETED (hoàn thành)
- Nhánh huỷ: CANCELLED_BY_SHOP / CANCELLED_BY_CUSTOMER / CANCELLED_NO_DRIVER / CANCELLED_BY_ADMIN
- Quy tắc: đơn treo ở PENDING_DRIVER > 2 phút → tự động chuyển đơn cho shipper khác (reassign) hoặc trả về NEW (tùy cấu hình).

### 17) Các ràng buộc & validation chính
- Giỏ hàng chỉ chứa món từ 1 nhà hàng; kiểm tra tồn/giờ mở; min order.
- Voucher: kiểm tra điều kiện, số lần dùng, thời hạn, stack rules.
- Thanh toán: timeout, retry, idempotency key; xác thực 3DS (nếu thẻ).
- Địa chỉ: nằm trong service_area; bắt buộc số ĐT hợp lệ.

### 18) Trải nghiệm lỗi & fallback
- Nếu menu/ảnh lỗi: hiển thị placeholder; cho phép thử lại.
- Nếu thanh toán thất bại: giữ đơn nháp, cho đổi phương thức.
- Nếu mất kết nối realtime: polling định kỳ 10–15s.

### 19) Analytics & đo lường
- Sự kiện (Shop): create_order, publish_product_request, unpublish_product, view_admin_post, view_shop_stats.
- Sự kiện (Shipper): view_available_orders, view_order_detail_before_accept, accept_order, update_status_picking|delivering|completed, complete_order, receive_notification.
- Sự kiện (Khách hàng): search_product, place_order_guest|logged_in, view_order_tracking, view_order_history.
- KPI: tỉ lệ duyệt sản phẩm, thời gian assign driver, tỉ lệ đơn treo > 2 phút, tỉ lệ hoàn thành, số đơn/ngày theo vai trò, đánh giá trung bình của shipper.

### 20) Chính sách hủy & hoàn tiền (tóm tắt)
- Trước NH xác nhận: hủy miễn phí.
- Sau NH chuẩn bị: có thể thu phí/không hoàn toàn.
- Không có tài xế: hoàn tiền toàn phần; ghi nhận trải nghiệm.

### 21) Bảo đảm chất lượng (Acceptance Criteria mẫu)
- Shop
  - Tạo đơn: bắt buộc địa chỉ gửi/nhận hợp lệ; khi tạo thành công, trạng thái = NEW.
  - Đăng sản phẩm: sau khi gửi duyệt, trạng thái = PENDING_APPROVAL; chỉ hiển thị khi = PUBLISHED; có thể UNPUBLISHED.
  - Theo dõi đơn: đơn thay đổi thành PENDING_DRIVER khi tạo; Shop thấy cập nhật realtime và lịch sử đầy đủ.
- Shipper
  - Danh sách đơn mới: chỉ chứa đơn chưa ai nhận; đơn đã nhận biến mất khỏi danh sách trong vòng ≤ 2s.
  - Nhận đơn: idempotent; nếu đã đủ 3 đơn đang xử lý thì từ chối kèm lỗi rõ ràng.
  - Cập nhật trạng thái: chỉ theo thứ tự PICKING → DELIVERING → COMPLETED; bấm Hoàn thành tính đúng phí cuối.
- Khách hàng
  - Đặt đơn: cho phép đặt khi chưa đăng nhập nếu điền thông tin người nhận đầy đủ; có mã theo dõi.
  - Xem đơn đã đặt: hiển thị tiến độ và lịch sử trạng thái chính xác.
- Hệ thống
  - Reassign đơn treo: nếu PENDING_DRIVER > 2 phút, tạo sự kiện reassign; đảm bảo không giao cho shipper đã từ chối.

### 22) Trường hợp biên (Edge cases)
- Địa chỉ sát biên vùng phục vụ, thay đổi vị trí sau khi đặt.
- Nhà hàng tạm đóng trong khi KH đang ở bước thanh toán.
- Hết tài xế: xếp hàng đợi tối đa N phút rồi hủy có thông báo bồi thường (nếu có).
- Voucher hết lượt dùng ngay tại thời điểm đặt; xử lý mềm và đề xuất thay thế.

### 23) Rủi ro & giảm thiểu
- Lạm dụng khuyến mãi: áp hạn mức, kiểm tra thiết bị, velocity rules, machine rules cơ bản.
- Tắc nghẽn giờ cao điểm: surge phí/ETA, điều phối vùng.
- Gián đoạn dịch vụ phụ trợ: circuit breaker, retry với backoff.

### 24) Roadmap gợi ý (rút gọn)
- P1: MVP đặt món, theo dõi, COD/ví, portal NH, app TX cơ bản.
- P2: Thanh toán thẻ/3DS, chat realtime, recommendation cơ bản.
- P3: Loyalty/điểm, đăng ký NH/TX tự phục vụ, analytics nâng cao.

### 25) Phụ lục: Thuật ngữ
- ETA: Estimated Time of Arrival; SLA: Service Level Agreement; COD: Cash on Delivery.
- Service area: vùng phục vụ; Surge: phụ phí giờ cao điểm; Idempotency: chống trùng lệnh.

