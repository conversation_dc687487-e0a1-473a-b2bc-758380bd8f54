import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import 'shop_model.dart';

part 'product_model.g.dart';

@JsonSerializable()
class ProductModel extends Equatable {
  final int id;
  @J<PERSON><PERSON><PERSON>(name: 'shop_id')
  final int? shopId;
  final String name;
  final String? description;
  @J<PERSON><PERSON><PERSON>(fromJson: _doubleFromString)
  final double price;
  final String? status;
  final String? image;
  final List<String>? images;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'category_id')
  final int? categoryId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_available')
  final bool isAvailable;
  final List<ProductVariantModel>? variants;
  final List<ProductToppingModel>? toppings;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'rejection_reason')
  final String? rejectionReason;
  @Json<PERSON>ey(name: 'reviewed_at')
  final DateTime? reviewedAt;
  @<PERSON><PERSON><PERSON>ey(name: 'reviewed_by')
  final int? reviewedBy;
  final ShopModel? shop;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final DateTime? createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final DateTime? updatedAt;

  const ProductModel({
    required this.id,
    this.shopId,
    required this.name,
    this.description,
    required this.price,
    this.status,
    this.image,
    this.images,
    this.categoryId,
    required this.isAvailable,
    this.variants,
    this.toppings,
    this.rejectionReason,
    this.reviewedAt,
    this.reviewedBy,
    this.shop,
    this.createdAt,
    this.updatedAt,
  });

  factory ProductModel.fromJson(Map<String, dynamic> json) =>
      _$ProductModelFromJson(json);
  Map<String, dynamic> toJson() => _$ProductModelToJson(this);

  @override
  List<Object?> get props => [
    id,
    shopId,
    name,
    description,
    price,
    status,
    image,
    images,
    categoryId,
    isAvailable,
    variants,
    toppings,
    rejectionReason,
    reviewedAt,
    reviewedBy,
    shop,
    createdAt,
    updatedAt,
  ];
}

@JsonSerializable()
class ProductVariantModel extends Equatable {
  final String id;
  final String name;
  @JsonKey(name: 'additional_price', fromJson: _doubleFromString)
  final double additionalPrice;
  @JsonKey(name: 'is_required')
  final bool isRequired;

  const ProductVariantModel({
    required this.id,
    required this.name,
    required this.additionalPrice,
    required this.isRequired,
  });

  factory ProductVariantModel.fromJson(Map<String, dynamic> json) =>
      _$ProductVariantModelFromJson(json);
  Map<String, dynamic> toJson() => _$ProductVariantModelToJson(this);

  @override
  List<Object?> get props => [id, name, additionalPrice, isRequired];
}

@JsonSerializable()
class ProductToppingModel extends Equatable {
  final String id;
  final String name;
  @JsonKey(fromJson: _doubleFromString)
  final double price;
  @JsonKey(name: 'is_available')
  final bool isAvailable;

  const ProductToppingModel({
    required this.id,
    required this.name,
    required this.price,
    required this.isAvailable,
  });

  factory ProductToppingModel.fromJson(Map<String, dynamic> json) =>
      _$ProductToppingModelFromJson(json);
  Map<String, dynamic> toJson() => _$ProductToppingModelToJson(this);

  @override
  List<Object?> get props => [id, name, price, isAvailable];
}

// Helper functions for JSON conversion
double _doubleFromString(dynamic value) {
  if (value == null) return 0.0;
  if (value is double) return value;
  if (value is int) return value.toDouble();
  if (value is String) return double.tryParse(value) ?? 0.0;
  return 0.0;
}

double? _nullableDoubleFromString(dynamic value) {
  if (value == null) return null;
  if (value is double) return value;
  if (value is int) return value.toDouble();
  if (value is String) return double.tryParse(value);
  return null;
}

// Note: CategoryModel is also defined in shop_model.dart
// This is a simplified version for product references
@JsonSerializable()
class ProductCategoryModel extends Equatable {
  final int id;
  final String name;

  const ProductCategoryModel({required this.id, required this.name});

  factory ProductCategoryModel.fromJson(Map<String, dynamic> json) =>
      _$ProductCategoryModelFromJson(json);
  Map<String, dynamic> toJson() => _$ProductCategoryModelToJson(this);

  @override
  List<Object?> get props => [id, name];
}
