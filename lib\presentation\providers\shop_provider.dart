import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/datasources/api_service_provider.dart';
import '../../data/models/shop_model.dart';
import '../../data/models/product_model.dart';
import '../../data/models/order_model.dart';
import '../../data/models/api_response_model.dart';
import '../../core/providers/loading_provider.dart';

// Shop profile provider
final shopProfileProvider = FutureProvider.autoDispose<ShopModel>((ref) async {
  final shopService = ref.read(shopApiServiceProvider);

  final response = await shopService.getProfile();

  if (response.success && response.data != null) {
    return ShopModel.fromJson(response.data);
  } else {
    throw Exception(response.message);
  }
});

// Shop products provider
final shopProductsProvider = FutureProvider.autoDispose
    .family<List<ProductModel>, ShopProductsParams>((ref, params) async {
      final shopService = ref.read(shopApiServiceProvider);

      final response = await shopService.getProducts(
        params.page,
        params.perPage,
        params.search,
        null, // category_id
        null, // status
      );

      if (response.success && response.data != null) {
        final products = (response.data!.data as List)
            .map((json) => ProductModel.fromJson(json))
            .toList();
        return products;
      } else {
        throw Exception(response.message);
      }
    });

// Shop orders provider
final shopOrdersProvider = FutureProvider.autoDispose
    .family<List<OrderModel>, ShopOrdersParams>((ref, params) async {
      final shopService = ref.read(shopApiServiceProvider);

      final response = await shopService.getOrders(
        params.page,
        params.perPage,
        params.status,
        params.dateFrom,
        params.dateTo,
      );

      if (response.success && response.data != null) {
        final orders = (response.data!.data as List)
            .map((json) => OrderModel.fromJson(json))
            .toList();
        return orders;
      } else {
        throw Exception(response.message);
      }
    });

// Shop stats provider
final shopStatsProvider = FutureProvider.autoDispose
    .family<ShopStatsModel, ShopStatsParams>((ref, params) async {
      final shopService = ref.read(shopApiServiceProvider);

      final response = await shopService.getStats(
        params.dateFrom,
        params.dateTo,
      );

      if (response.success && response.data != null) {
        return ShopStatsModel.fromJson(response.data);
      } else {
        throw Exception(response.message);
      }
    });

// Shop actions provider
final shopActionsProvider = Provider((ref) => ShopActions(ref));

class ShopActions {
  final Ref ref;

  ShopActions(this.ref);

  Future<ShopModel> updateProfile(Map<String, dynamic> profileData) async {
    final shopService = ref.read(shopApiServiceProvider);
    final loading = ref.read(loadingProvider.notifier);

    return loading.withLoading(LoadingKeys.updateProfile, () async {
      final response = await shopService.updateProfile(profileData);

      if (response.success && response.data != null) {
        // Invalidate profile cache
        ref.invalidate(shopProfileProvider);
        return ShopModel.fromJson(response.data);
      } else {
        throw Exception(response.message);
      }
    });
  }

  Future<ProductModel> createProduct(Map<String, dynamic> productData) async {
    final shopService = ref.read(shopApiServiceProvider);
    final loading = ref.read(loadingProvider.notifier);

    return loading.withLoading('create_product', () async {
      final response = await shopService.createProduct(productData);

      if (response.success && response.data != null) {
        // Invalidate products cache
        ref.invalidate(shopProductsProvider);
        return ProductModel.fromJson(response.data);
      } else {
        throw Exception(response.message);
      }
    });
  }

  Future<ProductModel> updateProduct(
    String productId,
    Map<String, dynamic> productData,
  ) async {
    final shopService = ref.read(shopApiServiceProvider);
    final loading = ref.read(loadingProvider.notifier);

    return loading.withLoading('update_product', () async {
      final response = await shopService.updateProduct(productId, productData);

      if (response.success && response.data != null) {
        // Invalidate products cache
        ref.invalidate(shopProductsProvider);
        return ProductModel.fromJson(response.data);
      } else {
        throw Exception(response.message);
      }
    });
  }

  Future<void> deleteProduct(String productId) async {
    final shopService = ref.read(shopApiServiceProvider);
    final loading = ref.read(loadingProvider.notifier);

    return loading.withLoading('delete_product', () async {
      final response = await shopService.deleteProduct(productId);

      if (!response.success) {
        throw Exception(response.message);
      }

      // Invalidate products cache
      ref.invalidate(shopProductsProvider);
    });
  }

  Future<void> toggleProductAvailability(String productId) async {
    final shopService = ref.read(shopApiServiceProvider);
    final loading = ref.read(loadingProvider.notifier);

    return loading.withLoading('toggle_product', () async {
      final response = await shopService.toggleProductAvailability(productId);

      if (!response.success) {
        throw Exception(response.message);
      }

      // Invalidate products cache
      ref.invalidate(shopProductsProvider);
    });
  }
}

// Parameter classes
class ShopProductsParams {
  final int? page;
  final int? perPage;
  final String? search;

  const ShopProductsParams({this.page, this.perPage, this.search});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ShopProductsParams &&
          runtimeType == other.runtimeType &&
          page == other.page &&
          perPage == other.perPage &&
          search == other.search;

  @override
  int get hashCode => page.hashCode ^ perPage.hashCode ^ search.hashCode;
}

class ShopOrdersParams {
  final int? page;
  final int? perPage;
  final String? status;
  final String? dateFrom;
  final String? dateTo;

  const ShopOrdersParams({
    this.page,
    this.perPage,
    this.status,
    this.dateFrom,
    this.dateTo,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ShopOrdersParams &&
          runtimeType == other.runtimeType &&
          page == other.page &&
          perPage == other.perPage &&
          status == other.status &&
          dateFrom == other.dateFrom &&
          dateTo == other.dateTo;

  @override
  int get hashCode =>
      page.hashCode ^
      perPage.hashCode ^
      status.hashCode ^
      dateFrom.hashCode ^
      dateTo.hashCode;
}

class ShopStatsParams {
  final String? dateFrom;
  final String? dateTo;

  const ShopStatsParams({this.dateFrom, this.dateTo});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ShopStatsParams &&
          runtimeType == other.runtimeType &&
          dateFrom == other.dateFrom &&
          dateTo == other.dateTo;

  @override
  int get hashCode => dateFrom.hashCode ^ dateTo.hashCode;
}
