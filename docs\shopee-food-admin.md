## Tài liệu mô tả chức năng Quản trị (Admin)

### 1) <PERSON><PERSON><PERSON> tiêu
- Quản trị và vận hành hệ thống giao hàng hiệu quả qua giao diện web.
- Hỗ trợ 2 loại tài khoản quản trị: <PERSON><PERSON> hệ thống và Shop Admin.

### 2) Loại tài khoản quản trị
- <PERSON><PERSON> hệ thống (System Admin)
  - Toàn quyền với Shop, Shipper, Đơn hàng, <PERSON><PERSON><PERSON> hình hệ thống, <PERSON><PERSON><PERSON> viết, <PERSON><PERSON><PERSON><PERSON> kê, <PERSON><PERSON> lý đơn treo.
- Shop Admin
  - Quản lý phạm vi shop của mình: thông tin shop, sản phẩm (nếu áp dụng), lịch sử đơn của shop, xem thống kê shop, xem bài viết; không chỉnh cấu hình hệ thống.

### 3) <PERSON><PERSON> quyền (tóm tắt)
- System Admin: CREATE/READ/UPDATE/DELETE (CRUD) trên Shop, Shipper; Đổi trạng thái đơn; Tạo đơn thay Shop; Cấu hình hệ thống; Bài viết; Thống kê toàn hệ thống; Xử lý đơn treo.
- Shop Admin: CRUD thông tin shop của mình; Xem lịch sử đơn của shop; Xem thống kê theo shop; Đọc bài viết; (Tùy chọn) tạo đơn thay shop của mình nếu được cấp quyền.

### 4) Chức năng chi tiết (Web)

4.1 Quản lý Shop
- Thêm/Sửa/Xóa thông tin shop: tên, địa chỉ, liên hệ, toạ độ (lat/lng), trạng thái hoạt động.
- Xem bản đồ vị trí (theo toạ độ) của shop.
- Xem lịch sử đơn hàng của shop: lọc theo trạng thái, thời gian.

4.2 Quản lý Shipper
- Xem danh sách shipper và trạng thái hoạt động (online/offline, số đơn đang chạy).
- Xem số đơn mỗi shipper theo ngày/tháng; lịch sử giao hàng; tỉ lệ hoàn thành/hủy.
- (System Admin) Tạm khoá/Mở khoá tài khoản shipper nếu cần.

4.3 Tạo đơn thay Shop
- Chọn shop nguồn (đối với System Admin) hoặc mặc định shop hiện tại (Shop Admin).
- Nhập địa chỉ gửi/nhận.
- Tự động tính phí khoảng cách và phí ship dựa trên bảng giá theo km.
- Tuỳ chọn thêm phụ phí (ngày lễ, mưa gió, lên lầu, cồng kềnh…).
- Tạo đơn thành công sẽ phát thông báo đơn mới đến tất cả shipper đủ điều kiện trong khu vực.

4.4 Quản lý Đơn
- Xem danh sách và chi tiết đơn hàng toàn hệ thống (System Admin) hoặc theo shop (Shop Admin).
- Xem và đổi trạng thái đơn hàng (theo quy tắc hệ thống): NEW → PENDING_DRIVER → PICKING → DELIVERING → COMPLETED; các nhánh huỷ.
- Ghi nhận lịch sử thay đổi trạng thái (audit trail: ai đổi, lúc nào, từ đâu đến đâu).

4.5 Cấu hình Hệ thống
- Bảng giá theo km: khoảng cách bậc thang, giá cơ bản, giá theo km tăng thêm, tối thiểu.
- Thiết lập phụ phí: ngày lễ, mưa gió, lên lầu, hàng cồng kềnh (theo % hoặc số tiền cố định, theo khung giờ/khu vực).
- Ngưỡng và quy tắc reassign đơn treo (ví dụ: > 2 phút ở PENDING_DRIVER sẽ reassign/chuyển broadcast lại).

4.6 Bài viết/Tin tức cho Shop
- Thêm/Sửa/Xoá bài viết hiển thị tới đối tượng Shop (hoặc toàn hệ thống nếu cần).
- Trình bày: tiêu đề, nội dung, ảnh minh hoạ, thời gian hiệu lực.
- Theo dõi lượt xem/đọc.

4.7 Thống kê Hệ thống
- Tổng số đơn theo ngày.
- Thống kê theo shop (số đơn/ngày, tỉ lệ hoàn thành, doanh thu ước tính).
- Doanh thu toàn hệ thống (ước tính theo phí ship + phụ phí) và các chỉ số vận hành (tỉ lệ hủy, thời gian nhận đơn…).

4.8 Xem và xử lý đơn treo quá lâu
- Danh sách đơn ở trạng thái PENDING_DRIVER quá ngưỡng thời gian.
- Hành động: chuyển/broadcast lại cho shipper khác; hoặc gán thủ công cho shipper cụ thể.
- Ghi log nguyên nhân, lần xử lý, người xử lý.

### 5) Luồng nghiệp vụ tiêu biểu

5.1 Tạo đơn thay shop (Web)
1) Chọn shop → 2) Nhập địa chỉ gửi/nhận → 3) Tự tính phí khoảng cách + phí ship → 4) Thêm phụ phí (tuỳ chọn) → 5) Tạo đơn → 6) Hệ thống gửi thông báo đơn mới đến shipper.

5.2 Xử lý đơn treo
1) Xem danh sách đơn treo > ngưỡng → 2) Chọn đơn → 3) Reassign/broadcast lại hoặc gán thủ công shipper → 4) Lưu lý do và hành động → 5) Theo dõi kết quả.

### 6) Tiêu chí chấp nhận (Acceptance Criteria)
- Quản lý shop
  - Cho phép CRUD đầy đủ; lưu toạ độ hợp lệ; hiển thị map preview khi có lat/lng.
  - Lịch sử đơn có bộ lọc thời gian và trạng thái; xuất CSV (nếu cần).
- Quản lý shipper
  - Danh sách cập nhật realtime số đơn đang chạy; có thể lọc theo khu vực, trạng thái.
  - Lịch sử giao hàng hiển thị đầy đủ thông tin giao/nhận và kết quả từng đơn.
- Tạo đơn thay shop
  - Phí ship tự tính dựa trên cấu hình; phụ phí được cộng đúng; tổng phí hiển thị rõ ràng.
  - Sau khi tạo đơn, shipper trong vùng nhận được thông báo trong vòng ≤ 2 giây.
- Quản lý đơn
  - Chỉ cho phép đổi trạng thái theo quy tắc hợp lệ; mọi thay đổi được audit.
- Cấu hình hệ thống
  - Bảng giá và phụ phí áp dụng ngay cho đơn mới; có lịch sử thay đổi cấu hình.
- Thống kê
  - Số liệu tổng hợp theo ngày chính xác; biểu đồ theo shop và toàn hệ thống.
- Đơn treo
  - Màn danh sách đơn treo hiển thị đúng tiêu chí; thao tác reassign ghi log đầy đủ và hiệu lực ngay.

### 7) Ghi chú kỹ thuật
- RBAC: phân quyền rõ cho System Admin vs Shop Admin; kiểm tra quyền ở cả API và UI.
- Idempotency: khi reassign/accept đơn để tránh xung đột.
- Tính phí: dùng service tính khoảng cách (Geo) + cấu hình bảng giá; phụ phí có thể theo rule điều kiện (thời tiết/ngày lễ/khung giờ).
- Thông báo: broadcast/push tới shipper đủ điều kiện (khu vực, trạng thái rảnh, < 3 đơn đang xử lý).
- Audit log: cho mọi thao tác quản trị quan trọng (đổi trạng thái, reassign, đổi cấu hình).

### 8) Mở rộng (tuỳ chọn)
- Bộ lọc nâng cao cho thống kê (khu vực, khung giờ cao điểm).
- Lịch sử thay đổi cấu hình với khả năng rollback.
- Phân quyền chi tiết cho Shop Admin theo nhóm tính năng (ví dụ: chỉ đọc thống kê).


### 9) API Admin (Laravel 12 + Filament)

- Auth & Versioning
  - Prefix: /api/v1/admin
  - Auth: Laravel Sanctum/Passport (Bearer), policy/permission via Filament & Gates/Policies

- Shop Management
  - GET   /shops?query=&page=    → liệt kê/lọc shop
  - POST  /shops                 → tạo shop
  - GET   /shops/{id}            → chi tiết shop (kèm toạ độ)
  - PUT   /shops/{id}            → cập nhật shop
  - DELETE/shops/{id}            → xoá shop
  - GET   /shops/{id}/orders?status=&from=&to= → lịch sử đơn của shop

- Shipper Management
  - GET   /shippers?status=&page=        → danh sách shipper + số đơn đang chạy
  - GET   /shippers/{id}/orders?from=&to=→ lịch sử đơn, thống kê
  - POST  /shippers/{id}/lock            → khoá
  - POST  /shippers/{id}/unlock          → mở khoá

- Create Order On Behalf of Shop
  - POST  /orders
    Body: { shop_id, pickup_address, dropoff_address, items[], surcharges[], note }
    Response: { id, fee_breakdown, status: "NEW" }

- Order Management
  - GET   /orders?status=&shop_id=&from=&to=
  - GET   /orders/{id}
  - POST  /orders/{id}/status { status }
  - POST  /orders/{id}/reassign { driver_id? } → reassign/broadcast

- System Config
  - GET   /configs/pricing           → bảng giá theo km
  - PUT   /configs/pricing           → cập nhật bảng giá
  - GET   /configs/surcharges        → danh sách phụ phí
  - PUT   /configs/surcharges        → cập nhật phụ phí
  - GET   /configs/assignment-rules  → ngưỡng treo, quy tắc assign
  - PUT   /configs/assignment-rules  → cập nhật

- Posts
  - GET   /posts?audience=SHOP
  - POST  /posts
  - GET   /posts/{id}
  - PUT   /posts/{id}
  - DELETE/posts/{id}

- Stats
  - GET   /stats/summary?date=YYYY-MM-DD
  - GET   /stats/by-shop?date=YYYY-MM-DD
  - GET   /stats/revenue?from=&to=

- Gợi ý triển khai Filament
  - Resources: ShopResource, ShipperResource, OrderResource, ConfigResource, PostResource, Stats pages
  - Actions: ReassignOrderAction, ChangeOrderStatusAction
  - Policies: ShopPolicy, ShipperPolicy, OrderPolicy; Spatie Permission cho vai trò System Admin / Shop Admin

