import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'order_model.g.dart';

@JsonSerializable()
class AddressModel extends Equatable {
  final String address;
  @J<PERSON><PERSON><PERSON>(fromJson: _doubleFromString)
  final double latitude;
  @J<PERSON><PERSON><PERSON>(fromJson: _doubleFromString)
  final double longitude;
  @Json<PERSON>ey(name: 'contact_name')
  final String contactName;
  @JsonKey(name: 'contact_phone')
  final String contactPhone;
  final String? note;

  const AddressModel({
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.contactName,
    required this.contactPhone,
    this.note,
  });

  factory AddressModel.fromJson(Map<String, dynamic> json) =>
      _$AddressModelFromJson(json);
  Map<String, dynamic> toJson() => _$AddressModelToJson(this);

  @override
  List<Object?> get props => [
    address,
    latitude,
    longitude,
    contactName,
    contactPhone,
    note,
  ];
}

@JsonSerializable()
class FeeBreakdownModel extends Equatable {
  @Json<PERSON>ey(fromJson: _doubleFromString)
  final double subtotal;
  @J<PERSON><PERSON>ey(name: 'delivery_fee', fromJson: _doubleFromString)
  final double deliveryFee;
  @J<PERSON>Key(name: 'service_fee', fromJson: _doubleFromString)
  final double serviceFee;
  @JsonKey(fromJson: _doubleFromString)
  final double discount;
  @JsonKey(fromJson: _doubleFromString)
  final double total;
  final List<dynamic>? surcharges;

  const FeeBreakdownModel({
    required this.subtotal,
    required this.deliveryFee,
    required this.serviceFee,
    required this.discount,
    required this.total,
    this.surcharges,
  });

  factory FeeBreakdownModel.fromJson(Map<String, dynamic> json) =>
      _$FeeBreakdownModelFromJson(json);
  Map<String, dynamic> toJson() => _$FeeBreakdownModelToJson(this);

  @override
  List<Object?> get props => [
    subtotal,
    deliveryFee,
    serviceFee,
    discount,
    total,
    surcharges,
  ];
}

@JsonSerializable()
class TimelineModel extends Equatable {
  final String status;
  final DateTime timestamp;
  final String note;
  @JsonKey(name: 'updated_by')
  final String? updatedBy;

  const TimelineModel({
    required this.status,
    required this.timestamp,
    required this.note,
    this.updatedBy,
  });

  factory TimelineModel.fromJson(Map<String, dynamic> json) =>
      _$TimelineModelFromJson(json);
  Map<String, dynamic> toJson() => _$TimelineModelToJson(this);

  @override
  List<Object?> get props => [status, timestamp, note, updatedBy];
}

@JsonSerializable()
class ReceiverInfoModel extends Equatable {
  final String name;
  final String phone;
  final String? email;

  const ReceiverInfoModel({
    required this.name,
    required this.phone,
    this.email,
  });

  factory ReceiverInfoModel.fromJson(Map<String, dynamic> json) =>
      _$ReceiverInfoModelFromJson(json);
  Map<String, dynamic> toJson() => _$ReceiverInfoModelToJson(this);

  @override
  List<Object?> get props => [name, phone, email];
}

@JsonSerializable()
class OrderItemModel extends Equatable {
  @JsonKey(name: 'product_id')
  final int productId;
  @JsonKey(name: 'product_name')
  final String productName;
  final int quantity;
  @JsonKey(name: 'unit_price', fromJson: _doubleFromString)
  final double unitPrice;
  @JsonKey(name: 'total_price', fromJson: _doubleFromString)
  final double totalPrice;
  @JsonKey(name: 'selected_variant')
  final String? selectedVariant;
  @JsonKey(name: 'selected_toppings')
  final List<String>? selectedToppings;
  final String? note;

  const OrderItemModel({
    required this.productId,
    required this.productName,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
    this.selectedVariant,
    this.selectedToppings,
    this.note,
  });

  factory OrderItemModel.fromJson(Map<String, dynamic> json) =>
      _$OrderItemModelFromJson(json);
  Map<String, dynamic> toJson() => _$OrderItemModelToJson(this);

  @override
  List<Object?> get props => [
    productId,
    productName,
    quantity,
    unitPrice,
    totalPrice,
    selectedVariant,
    selectedToppings,
    note,
  ];
}

@JsonSerializable()
class OrderModel extends Equatable {
  final int id;
  @JsonKey(name: 'customer_id')
  final int? customerId;
  @JsonKey(name: 'shop_id')
  final int? shopId;
  @JsonKey(name: 'driver_id')
  final int? driverId;
  final List<OrderItemModel> items; // typed items per API
  @JsonKey(name: 'pickup_address')
  final AddressModel pickupAddress;
  @JsonKey(name: 'dropoff_address')
  final AddressModel dropoffAddress;
  @JsonKey(name: 'fee_breakdown')
  final FeeBreakdownModel feeBreakdown;
  final String status;
  final String? note;
  @JsonKey(name: 'payment_method')
  final String? paymentMethod;
  @JsonKey(name: 'payment_status')
  final String? paymentStatus;
  final List<TimelineModel> timeline;
  @JsonKey(name: 'receiver_info')
  final ReceiverInfoModel? receiverInfo;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;
  final OrderShopModel? shop; // Related shop data

  // Computed properties
  String get receiverName => receiverInfo?.name ?? '';
  String get receiverPhone => receiverInfo?.phone ?? '';
  double get totalAmount => feeBreakdown.total;

  const OrderModel({
    required this.id,
    this.customerId,
    this.shopId,
    this.driverId,
    required this.items,
    required this.pickupAddress,
    required this.dropoffAddress,
    required this.feeBreakdown,
    required this.status,
    this.note,
    this.paymentMethod,
    this.paymentStatus,
    required this.timeline,
    this.receiverInfo,
    required this.createdAt,
    required this.updatedAt,
    this.shop,
  });

  factory OrderModel.fromJson(Map<String, dynamic> json) =>
      _$OrderModelFromJson(json);
  Map<String, dynamic> toJson() => _$OrderModelToJson(this);

  // Helper methods
  bool get isAssignable => status == 'PENDING_DRIVER' && driverId == null;
  bool get isInProgress =>
      ['NEW', 'PENDING_DRIVER', 'PICKING', 'DELIVERING'].contains(status);
  bool get isCompleted => status == 'COMPLETED';
  bool get isCancelled => status.startsWith('CANCELLED');

  @override
  List<Object?> get props => [
    id,
    customerId,
    shopId,
    driverId,
    items,
    pickupAddress,
    dropoffAddress,
    feeBreakdown,
    status,
    note,
    paymentMethod,
    paymentStatus,
    timeline,
    receiverInfo,
    createdAt,
    updatedAt,
    shop,
  ];
}

@JsonSerializable()
class OrderShopModel extends Equatable {
  final int id;
  final String name;
  final String address;
  final String? phone;
  final String? email;
  @JsonKey(name: 'rating', fromJson: _nullableDoubleFromString)
  final double? rating;
  @JsonKey(name: 'review_count', fromJson: _nullableIntFromAny)
  final int? reviewCount;
  @JsonKey(name: 'created_at')
  final DateTime? createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime? updatedAt;

  const OrderShopModel({
    required this.id,
    required this.name,
    required this.address,
    this.phone,
    this.email,
    this.rating,
    this.reviewCount,
    this.createdAt,
    this.updatedAt,
  });

  factory OrderShopModel.fromJson(Map<String, dynamic> json) =>
      _$OrderShopModelFromJson(json);
  Map<String, dynamic> toJson() => _$OrderShopModelToJson(this);

  @override
  List<Object?> get props => [
    id,
    name,
    address,
    phone,
    email,
    rating,
    reviewCount,
    createdAt,
    updatedAt,
  ];
}

int? _nullableIntFromAny(dynamic value) {
  if (value == null) return null;
  if (value is int) return value;
  if (value is double) return value.toInt();
  if (value is String) {
    final i = int.tryParse(value);
    if (i != null) return i;
    final d = double.tryParse(value);
    if (d != null) return d.toInt();
  }
  return null;
}

double? _nullableDoubleFromString(dynamic value) {
  if (value == null) return null;
  if (value is double) return value;
  if (value is int) return value.toDouble();
  if (value is String) return double.tryParse(value);
  return null;
}

// Helper functions for JSON conversion
double _doubleFromString(dynamic value) {
  if (value == null) return 0.0;
  if (value is double) return value;
  if (value is int) return value.toDouble();
  if (value is String) return double.tryParse(value) ?? 0.0;
  return 0.0;
}
