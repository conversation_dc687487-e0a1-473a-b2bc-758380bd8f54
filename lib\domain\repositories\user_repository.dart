import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../data/models/user_model.dart';
import '../../data/models/auth_response_model.dart';

abstract class UserRepository {
  Future<Either<Failure, AuthResponseModel>> register(
    String name,
    String email,
    String phone,
    String password,
    String role,
  );
  Future<Either<Failure, AuthResponseModel>> login(
    String email,
    String password,
  );
  Future<Either<Failure, void>> logout();
  Future<Either<Failure, UserModel>> getCurrentUser();
}
