import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../data/models/product_model.dart';
import '../../data/models/shop_model.dart';
import '../repositories/product_repository.dart';

class GetProductsUseCase {
  final ProductRepository repository;

  GetProductsUseCase(this.repository);

  Future<Either<Failure, List<ProductModel>>> call() async {
    return await repository.getProducts();
  }
}

class GetProductByIdUseCase {
  final ProductRepository repository;

  GetProductByIdUseCase(this.repository);

  Future<Either<Failure, ProductModel?>> call(String id) async {
    return await repository.getProductById(id);
  }
}

class GetProductsByShopUseCase {
  final ProductRepository repository;

  GetProductsByShopUseCase(this.repository);

  Future<Either<Failure, List<ProductModel>>> call(String shopId) async {
    return await repository.getProductsByShop(shopId);
  }
}

class SearchProductsUseCase {
  final ProductRepository repository;

  SearchProductsUseCase(this.repository);

  Future<Either<Failure, List<ProductModel>>> call(String query) async {
    return await repository.searchProducts(query);
  }
}

class GetCategoriesUseCase {
  final ProductRepository repository;

  GetCategoriesUseCase(this.repository);

  Future<Either<Failure, List<CategoryModel>>> call() async {
    return await repository.getCategories();
  }
}
