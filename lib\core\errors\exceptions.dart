class ServerException implements Exception {
  final String message;
  final int? statusCode;
  
  const ServerException(this.message, [this.statusCode]);
}

class NetworkException implements Exception {
  final String message;
  
  const NetworkException(this.message);
}

class CacheException implements Exception {
  final String message;
  
  const CacheException(this.message);
}

class ValidationException implements Exception {
  final String message;
  final Map<String, List<String>>? errors;
  
  const ValidationException(this.message, [this.errors]);
}

class AuthenticationException implements Exception {
  final String message;
  
  const AuthenticationException(this.message);
}

class PermissionException implements Exception {
  final String message;
  
  const PermissionException(this.message);
}

class LocationException implements Exception {
  final String message;
  
  const LocationException(this.message);
}

class PaymentException implements Exception {
  final String message;
  final String? errorCode;
  
  const PaymentException(this.message, [this.errorCode]);
}
