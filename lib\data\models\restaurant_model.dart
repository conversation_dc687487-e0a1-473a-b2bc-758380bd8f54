import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'restaurant_model.g.dart';

@JsonSerializable()
class RestaurantModel extends Equatable {
  final String id;
  final String name;
  final String description;
  final String address;
  @J<PERSON><PERSON><PERSON>(fromJson: _doubleFromString)
  final double latitude;
  @J<PERSON><PERSON>ey(fromJson: _doubleFromString)
  final double longitude;
  final String? phone;
  final String? email;
  final String? image;
  final List<String> images;
  @<PERSON><PERSON><PERSON><PERSON>(fromJson: _doubleFromString)
  final double rating;
  final int reviewCount;
  final String? businessHours;
  final bool isOpen;
  final bool isActive;
  final int estimatedDeliveryTime; // in minutes
  @JsonKey(fromJson: _doubleFromString)
  final double deliveryFee;
  @J<PERSON><PERSON>ey(fromJson: _doubleFromString)
  final double minimumOrder;
  final List<String> categoryIds;
  final DateTime createdAt;
  final DateTime updatedAt;

  const RestaurantModel({
    required this.id,
    required this.name,
    required this.description,
    required this.address,
    required this.latitude,
    required this.longitude,
    this.phone,
    this.email,
    this.image,
    required this.images,
    required this.rating,
    required this.reviewCount,
    this.businessHours,
    required this.isOpen,
    required this.isActive,
    required this.estimatedDeliveryTime,
    required this.deliveryFee,
    required this.minimumOrder,
    required this.categoryIds,
    required this.createdAt,
    required this.updatedAt,
  });

  factory RestaurantModel.fromJson(Map<String, dynamic> json) =>
      _$RestaurantModelFromJson(json);
  Map<String, dynamic> toJson() => _$RestaurantModelToJson(this);

  @override
  List<Object?> get props => [
    id,
    name,
    description,
    address,
    latitude,
    longitude,
    phone,
    email,
    image,
    images,
    rating,
    reviewCount,
    businessHours,
    isOpen,
    isActive,
    estimatedDeliveryTime,
    deliveryFee,
    minimumOrder,
    categoryIds,
    createdAt,
    updatedAt,
  ];
}

@JsonSerializable()
class VoucherModel extends Equatable {
  final String id;
  final String code;
  final String title;
  final String description;
  final String type; // PERCENTAGE, FIXED_AMOUNT, FREE_DELIVERY, COMBO
  @JsonKey(fromJson: _doubleFromString)
  final double value;
  @JsonKey(fromJson: _nullableDoubleFromString)
  final double? minimumOrder;
  @JsonKey(fromJson: _nullableDoubleFromString)
  final double? maximumDiscount;
  final DateTime validFrom;
  final DateTime validTo;
  final int? usageLimit;
  final int usedCount;
  final bool isActive;
  final List<String>? applicableRestaurants;
  final List<String>? applicableCategories;
  final String? userType; // NEW_USER, ALL, SPECIFIC

  const VoucherModel({
    required this.id,
    required this.code,
    required this.title,
    required this.description,
    required this.type,
    required this.value,
    this.minimumOrder,
    this.maximumDiscount,
    required this.validFrom,
    required this.validTo,
    this.usageLimit,
    required this.usedCount,
    required this.isActive,
    this.applicableRestaurants,
    this.applicableCategories,
    this.userType,
  });

  factory VoucherModel.fromJson(Map<String, dynamic> json) =>
      _$VoucherModelFromJson(json);
  Map<String, dynamic> toJson() => _$VoucherModelToJson(this);

  @override
  List<Object?> get props => [
    id,
    code,
    title,
    description,
    type,
    value,
    minimumOrder,
    maximumDiscount,
    validFrom,
    validTo,
    usageLimit,
    usedCount,
    isActive,
    applicableRestaurants,
    applicableCategories,
    userType,
  ];
}

@JsonSerializable()
class PostModel extends Equatable {
  final String id;
  final String title;
  final String content;
  final String? image;
  final String audience; // SHOP, ALL
  final String createdBy;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;

  const PostModel({
    required this.id,
    required this.title,
    required this.content,
    this.image,
    required this.audience,
    required this.createdBy,
    required this.createdAt,
    required this.updatedAt,
    required this.isActive,
  });

  factory PostModel.fromJson(Map<String, dynamic> json) =>
      _$PostModelFromJson(json);
  Map<String, dynamic> toJson() => _$PostModelToJson(this);

  @override
  List<Object?> get props => [
    id,
    title,
    content,
    image,
    audience,
    createdBy,
    createdAt,
    updatedAt,
    isActive,
  ];
}

// Helper functions for JSON conversion
double _doubleFromString(dynamic value) {
  if (value == null) return 0.0;
  if (value is double) return value;
  if (value is int) return value.toDouble();
  if (value is String) return double.tryParse(value) ?? 0.0;
  return 0.0;
}

double? _nullableDoubleFromString(dynamic value) {
  if (value == null) return null;
  if (value is double) return value;
  if (value is int) return value.toDouble();
  if (value is String) return double.tryParse(value);
  return null;
}
