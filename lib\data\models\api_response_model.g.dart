// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'api_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BaseApiResponseModel _$BaseApiResponseModelFromJson(
        Map<String, dynamic> json) =>
    BaseApiResponseModel(
      success: json['success'] as bool,
      message: json['message'] as String,
      data: json['data'],
      errors: json['errors'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$BaseApiResponseModelToJson(
        BaseApiResponseModel instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'data': instance.data,
      'errors': instance.errors,
    };

SimpleApiResponseModel _$SimpleApiResponseModelFromJson(
        Map<String, dynamic> json) =>
    SimpleApiResponseModel(
      success: json['success'] as bool,
      message: json['message'] as String,
      errors: json['errors'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$SimpleApiResponseModelToJson(
        SimpleApiResponseModel instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'errors': instance.errors,
    };

PaginatedResponseModel _$PaginatedResponseModelFromJson(
        Map<String, dynamic> json) =>
    PaginatedResponseModel(
      success: json['success'] as bool,
      message: json['message'] as String,
      data: json['data'] == null
          ? null
          : PaginatedDataModel.fromJson(json['data'] as Map<String, dynamic>),
      errors: json['errors'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$PaginatedResponseModelToJson(
        PaginatedResponseModel instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'data': instance.data,
      'errors': instance.errors,
    };

PaginatedDataModel _$PaginatedDataModelFromJson(Map<String, dynamic> json) =>
    PaginatedDataModel(
      currentPage: _nullableIntFromAny(json['current_page']),
      data: json['data'] as List<dynamic>?,
      firstPageUrl: json['first_page_url'] as String?,
      from: _nullableIntFromAny(json['from']),
      lastPage: _nullableIntFromAny(json['last_page']),
      lastPageUrl: json['last_page_url'] as String?,
      nextPageUrl: json['next_page_url'] as String?,
      path: json['path'] as String?,
      perPage: _nullableIntFromAny(json['per_page']),
      prevPageUrl: json['prev_page_url'] as String?,
      to: _nullableIntFromAny(json['to']),
      total: _nullableIntFromAny(json['total']),
    );

Map<String, dynamic> _$PaginatedDataModelToJson(PaginatedDataModel instance) =>
    <String, dynamic>{
      'current_page': instance.currentPage,
      'data': instance.data,
      'first_page_url': instance.firstPageUrl,
      'from': instance.from,
      'last_page': instance.lastPage,
      'last_page_url': instance.lastPageUrl,
      'next_page_url': instance.nextPageUrl,
      'path': instance.path,
      'per_page': instance.perPage,
      'prev_page_url': instance.prevPageUrl,
      'to': instance.to,
      'total': instance.total,
    };
