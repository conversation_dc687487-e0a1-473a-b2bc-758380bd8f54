import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import '../models/api_response_model.dart';

part 'shop_api_service.g.dart';

@RestApi()
abstract class ShopApiService {
  factory ShopApiService(Dio dio, {String baseUrl}) = _ShopApiService;

  // Profile
  @GET('/shops/profile')
  Future<BaseApiResponseModel> getProfile();

  @PUT('/shops/profile')
  Future<BaseApiResponseModel> updateProfile(
    @Body() Map<String, dynamic> profileData,
  );

  // Products
  @GET('/shops/products')
  Future<PaginatedResponseModel> getProducts(
    @Query('page') int? page,
    @Query('per_page') int? perPage,
    @Query('search') String? search,
    @Query('category_id') int? categoryId,
    @Query('status') String? status,
  );

  @POST('/shops/products')
  Future<BaseApiResponseModel> createProduct(
    @Body() Map<String, dynamic> productData,
  );

  @PUT('/shops/products/{id}')
  Future<BaseApiResponseModel> updateProduct(
    @Path('id') String productId,
    @Body() Map<String, dynamic> productData,
  );

  @DELETE('/shops/products/{id}')
  Future<SimpleApiResponseModel> deleteProduct(@Path('id') String productId);

  @POST('/shops/products/{id}/toggle-availability')
  Future<SimpleApiResponseModel> toggleProductAvailability(
    @Path('id') String productId,
  );

  // Orders
  @GET('/shops/orders')
  Future<PaginatedResponseModel> getOrders(
    @Query('page') int? page,
    @Query('per_page') int? perPage,
    @Query('status') String? status,
    @Query('date_from') String? dateFrom,
    @Query('date_to') String? dateTo,
  );

  @GET('/shops/orders/{id}')
  Future<BaseApiResponseModel> getOrderDetail(@Path('id') String orderId);

  // Stats
  @GET('/shops/stats')
  Future<BaseApiResponseModel> getStats(
    @Query('date_from') String? dateFrom,
    @Query('date_to') String? dateTo,
  );
}
