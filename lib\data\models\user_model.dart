import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import 'driver_model.dart';

part 'user_model.g.dart';

@JsonSerializable()
class UserProfileModel extends Equatable {
  final String? address;
  @J<PERSON><PERSON><PERSON>(fromJson: _nullableDoubleFromString)
  final double? latitude;
  @JsonKey(fromJson: _nullableDoubleFromString)
  final double? longitude;
  @JsonKey(name: 'date_of_birth')
  final String? dateOfBirth;
  final String? gender;
  @Json<PERSON>ey(name: 'shop_profile')
  final ShopProfileModel? shopProfile;
  @J<PERSON><PERSON><PERSON>(name: 'shipper_profile')
  final ShipperProfileModel? shipperProfile;

  const UserProfileModel({
    this.address,
    this.latitude,
    this.longitude,
    this.dateOfBirth,
    this.gender,
    this.shopProfile,
    this.shipperProfile,
  });

  factory UserProfileModel.fromJson(Map<String, dynamic> json) =>
      _$UserProfileModelFromJson(json);
  Map<String, dynamic> toJson() => _$UserProfileModelToJson(this);

  @override
  List<Object?> get props => [
    address,
    latitude,
    longitude,
    dateOfBirth,
    gender,
    shopProfile,
    shipperProfile,
  ];
}

@JsonSerializable()
class ShopProfileModel extends Equatable {
  @JsonKey(name: 'business_name')
  final String? businessName;
  @JsonKey(name: 'business_address')
  final String? businessAddress;
  @JsonKey(name: 'business_phone')
  final String? businessPhone;
  @JsonKey(name: 'business_email')
  final String? businessEmail;
  @JsonKey(name: 'business_latitude')
  final double? businessLatitude;
  @JsonKey(name: 'business_longitude')
  final double? businessLongitude;
  @JsonKey(name: 'business_hours')
  final String? businessHours;
  @JsonKey(name: 'is_verified')
  final bool? isVerified;

  const ShopProfileModel({
    this.businessName,
    this.businessAddress,
    this.businessPhone,
    this.businessEmail,
    this.businessLatitude,
    this.businessLongitude,
    this.businessHours,
    this.isVerified,
  });

  factory ShopProfileModel.fromJson(Map<String, dynamic> json) =>
      _$ShopProfileModelFromJson(json);
  Map<String, dynamic> toJson() => _$ShopProfileModelToJson(this);

  @override
  List<Object?> get props => [
    businessName,
    businessAddress,
    businessPhone,
    businessEmail,
    businessLatitude,
    businessLongitude,
    businessHours,
    isVerified,
  ];
}

@JsonSerializable()
class ShipperProfileModel extends Equatable {
  @JsonKey(name: 'vehicle_type')
  final String? vehicleType;
  @JsonKey(name: 'license_plate')
  final String? licensePlate;
  final double? rating;
  @JsonKey(name: 'total_orders')
  final int? totalOrders;
  @JsonKey(name: 'active_order_count')
  final int? activeOrderCount;
  @JsonKey(name: 'is_online')
  final bool? isOnline;
  @JsonKey(name: 'current_location')
  final String? currentLocation;

  const ShipperProfileModel({
    this.vehicleType,
    this.licensePlate,
    this.rating,
    this.totalOrders,
    this.activeOrderCount,
    this.isOnline,
    this.currentLocation,
  });

  factory ShipperProfileModel.fromJson(Map<String, dynamic> json) =>
      _$ShipperProfileModelFromJson(json);
  Map<String, dynamic> toJson() => _$ShipperProfileModelToJson(this);

  @override
  List<Object?> get props => [
    vehicleType,
    licensePlate,
    rating,
    totalOrders,
    activeOrderCount,
    isOnline,
    currentLocation,
  ];
}

@JsonSerializable()
class UserModel extends Equatable {
  final int id;
  final String name;
  final String email;
  final String? phone;
  final String role; // SHOP, SHIPPER, CUSTOMER, ADMIN
  final String? avatar;
  @JsonKey(name: 'is_active')
  final bool? isActive;
  @JsonKey(name: 'created_at')
  final DateTime? createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime? updatedAt;
  final DriverModel? driver; // For shipper users
  final UserProfileModel? profile;

  const UserModel({
    required this.id,
    required this.name,
    required this.email,
    this.phone,
    required this.role,
    this.avatar,
    this.isActive,
    this.createdAt,
    this.updatedAt,
    this.driver,
    this.profile,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);
  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  // Helper methods
  bool get isShipper => role == 'SHIPPER';
  bool get isShop => role == 'SHOP';
  bool get isCustomer => role == 'CUSTOMER';
  bool get isAdmin => role == 'ADMIN';

  @override
  List<Object?> get props => [
    id,
    name,
    email,
    phone,
    role,
    avatar,
    isActive,
    createdAt,
    updatedAt,
    driver,
    profile,
  ];
}

// Helper functions for JSON conversion
double? _nullableDoubleFromString(dynamic value) {
  if (value == null) return null;
  if (value is double) return value;
  if (value is int) return value.toDouble();
  if (value is String) return double.tryParse(value);
  return null;
}
