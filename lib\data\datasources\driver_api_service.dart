import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import '../models/order_model.dart';
import '../models/driver_stats_model.dart';
import '../models/api_response_model.dart';

part 'driver_api_service.g.dart';

@RestApi()
abstract class DriverApiService {
  factory DriverApiService(Dio dio, {String baseUrl}) = _DriverApiService;

  @GET('/drivers/orders/available')
  Future<PaginatedResponseModel> getAvailableOrders(
    @Query('page') int? page,
    @Query('per_page') int? perPage,
    @Query('pickup_latitude') double? pickupLatitude,
    @Query('pickup_longitude') double? pickupLongitude,
    @Query('max_distance') double? maxDistance,
  );

  @GET('/drivers/orders/{id}')
  Future<BaseApiResponseModel> getOrderDetail(@Path('id') String orderId);

  @POST('/drivers/orders/{id}/accept')
  Future<SimpleApiResponseModel> acceptOrder(@Path('id') String orderId);

  @POST('/drivers/orders/{id}/status')
  Future<SimpleApiResponseModel> updateOrderStatus(
    @Path('id') String orderId,
    @Body() Map<String, dynamic> statusData,
  );

  @GET('/drivers/stats')
  Future<BaseApiResponseModel> getDriverStats(
    @Query('date_from') String? dateFrom,
    @Query('date_to') String? dateTo,
  );
}
