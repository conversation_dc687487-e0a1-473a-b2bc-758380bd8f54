# Cấu H<PERSON>nh <PERSON> - Shipper19

## Tổng Quan
Dự án đã được cấu hình lại với bảng màu VÀNG-ĐEN mới theo yêu cầu:

### 🎨 **Bảng <PERSON> (Theme Vàng-Đen):**
1. **Nền**: <PERSON><PERSON><PERSON> (#FFEB3B)
2. **Chữ**: <PERSON><PERSON> (#0D2949)
3. **Nền Input**: Trắng (#FFFFFF)
4. **Chữ Input**: <PERSON><PERSON> (#000000)
5. **Nền Button**: <PERSON><PERSON> (#070000)
6. **Chữ Button**: V<PERSON>ng (#FFEB3B)
7. **Tab**: <PERSON><PERSON><PERSON> v<PERSON><PERSON> (#FFE082), chữ đen xanh (#0D2949)
8. **Tab Active**: N<PERSON><PERSON> x<PERSON>h l<PERSON> đậm (#225A33), chữ xanh l<PERSON> đậ<PERSON> (#225A33)
9. **Logo**: Sử dụng ảnh logo.png, không có nền (transparent)

## Logo Mới
Đã cập nhật logo từ icon mặc định sang ảnh logo.png:
- **File logo**: `assets/images/logo.png`
- **Widget logo**: `lib/presentation/widgets/app_logo.dart`
- **Tính năng**: Tự động fallback về icon nếu không load được ảnh
- **Vị trí hiển thị**: Splash screen, Login page, App bars

### Các widget logo:
- `AppLogo()` - Logo cơ bản
- `AppLogo.large()` - Logo lớn cho splash screen
- `AppLogo.small()` - Logo nhỏ cho app bar
- `AnimatedAppLogo()` - Logo có animation
- `AppBarLogo()` - Logo cho app bar với hover effect
- `CustomAppBar()` - App bar tùy chỉnh với logo

## Files Đã Thay Đổi

### 1. Cấu Hình Màu Chính
- **`lib/core/constants/app_colors.dart`** (Mới): File định nghĩa tất cả màu sắc của ứng dụng
- **`lib/main.dart`**: Cập nhật theme để sử dụng màu sắc mới

### 2. Widget Tùy Chỉnh
- **`lib/presentation/widgets/custom_widgets.dart`** (Mới): Các widget tùy chỉnh với màu sắc đúng thiết kế
  - `CustomTextField`: Input field với màu trắng và chữ xám
  - `CustomButton`: Button với nền đen và chữ trắng
  - `CustomOutlineButton`: Button viền với màu tùy chỉnh
  - `CustomCard`: Card với màu nền trắng
  - `CustomSearchBar`: Thanh tìm kiếm với màu sắc đúng thiết kế

### 3. Các Trang Đã Cập Nhật
- **`lib/presentation/pages/auth/login_page.dart`**: Cập nhật màu sắc icon/title từ vàng sang đen, sửa layout overflow
- **`lib/presentation/pages/auth/register_page.dart`**: Import màu sắc mới
- **`lib/presentation/pages/auth/forgot_password_page.dart`**: Sửa màu icon và text từ vàng sang đen
- **`lib/presentation/pages/auth/splash_page.dart`**: Sửa màu icon từ cam sang đen
- **`lib/presentation/pages/customer/customer_home_page.dart`**: Sửa màu giá sản phẩm từ vàng sang đen, sửa overflow
- **`lib/presentation/pages/customer/search_page.dart`**: Cập nhật input field màu sắc
- **`lib/presentation/pages/customer/order_history_page.dart`**: Sửa màu tổng tiền từ vàng sang đen
- **`lib/presentation/pages/customer/restaurant_detail_page.dart`**: Sửa màu giá sản phẩm từ vàng sang đen
- **`lib/presentation/pages/customer/cart_page.dart`**: Sửa màu tổng tiền từ vàng sang đen
- **`lib/presentation/pages/shop/shop_home_page.dart`**: Cập nhật màu trạng thái đơn hàng và text
- **`lib/presentation/pages/shop/shop_products_page.dart`**: Sửa màu giá sản phẩm từ vàng sang đen
- **`lib/presentation/pages/shipper/stats_page.dart`**: Sửa màu icon từ vàng sang đen

## Chi Tiết Màu Sắc

### Màu Chính (Primary Colors)
```dart
static const Color primary = Color(0xFFFFEB3B); // Vàng sáng
static const Color primaryDark = Color(0xFFFBC02D); // Vàng đậm
static const Color primaryLight = Color(0xFFFFF59D); // Vàng nhạt
```

### Màu Nền (Background Colors)
```dart
static const Color background = Color(0xFFFFEB3B); // Nền vàng sáng
static const Color surface = Color(0xFFFFFFFF); // Bề mặt trắng
static const Color scaffoldBackground = Color(0xFFFFEB3B); // Nền scaffold vàng
```

### Màu Chữ (Text Colors)
```dart
static const Color textPrimary = Color(0xFF000000); // Chữ đen
static const Color textSecondary = Color(0xFF424242); // Chữ xám đậm
static const Color textHint = Color(0xFF757575); // Chữ gợi ý xám
```

### Màu Input (Input Colors)
```dart
static const Color inputBackground = Color(0xFFFFFFFF); // Nền input trắng
static const Color inputText = Color(0xFF757575); // Chữ trong input xám
static const Color inputHint = Color(0xFF9E9E9E); // Chữ gợi ý xám nhạt
static const Color inputBorder = Color(0xFFE0E0E0); // Viền input xám nhạt
static const Color inputFocusedBorder = Color(0xFF000000); // Viền focus đen
```

### Màu Button (Button Colors)
```dart
static const Color buttonBackground = Color(0xFF000000); // Nền button đen
static const Color buttonText = Color(0xFFFFFFFF); // Chữ button trắng
static const Color buttonDisabled = Color(0xFF616161); // Button vô hiệu hóa
```

### Màu Trạng Thái (Status Colors)
```dart
static const Color success = Color(0xFF4CAF50); // Xanh lá thành công
static const Color warning = Color(0xFFFF9800); // Cam cảnh báo
static const Color error = Color(0xFFF44336); // Đỏ lỗi
static const Color info = Color(0xFF2196F3); // Xanh dương thông tin
```

## Cách Sử Dụng

### 1. Import màu sắc
```dart
import '../../../core/constants/app_colors.dart';
```

### 2. Sử dụng màu trong widget
```dart
Container(
  color: AppColors.background,
  child: Text(
    'Hello World',
    style: TextStyle(color: AppColors.textPrimary),
  ),
)
```

### 3. Sử dụng widget tùy chỉnh
```dart
// TextField tùy chỉnh
CustomTextField(
  hintText: 'Nhập email',
  controller: emailController,
)

// Button tùy chỉnh
CustomButton(
  text: 'Đăng nhập',
  onPressed: () => handleLogin(),
)

// Search bar tùy chỉnh
CustomSearchBar(
  hintText: 'Tìm kiếm...',
  onChanged: (value) => handleSearch(value),
)
```

## Kiểm Tra Kết Quả

Ứng dụng đã được test và chạy thành công với:
- ✅ Màu nền vàng sáng trên toàn bộ ứng dụng
- ✅ Màu chữ đen rõ ràng, dễ đọc trên tất cả nền trắng
- ✅ Ô input màu trắng với chữ xám bên trong
- ✅ Button nền đen với chữ trắng nổi bật
- ✅ Các màu trạng thái phù hợp (xanh, cam, đỏ, xanh dương)
- ✅ Layout responsive với các lỗi overflow đã được sửa
- ✅ Tất cả text trên nền trắng (Card, Container) đều sử dụng màu đen thay vì màu vàng
- ✅ Icon và title trong các trang auth đã chuyển từ màu vàng sang màu đen
- ✅ Giá sản phẩm trong tất cả các trang đã chuyển từ màu vàng sang màu đen
- ✅ Tab active có màu đen nổi bật với indicator dày 3px
- ✅ Nút thêm vào giỏ hàng (icon add) đã chuyển từ nền vàng sang nền đen với icon trắng
- ✅ Tất cả ElevatedButton đều có nền đen và chữ trắng theo thiết kế

## Lưu Ý
- Tất cả màu sắc đều được định nghĩa trong `AppColors` class để dễ quản lý
- Theme được cấu hình tự động áp dụng màu sắc cho toàn bộ ứng dụng
- Các widget tùy chỉnh đảm bảo tính nhất quán về màu sắc
- Màu sắc được thiết kế để đảm bảo độ tương phản tốt và accessibility
